// To parse this JSON data, do
//
//     final workFlowModelResponseModel = workFlowModelResponseModelFromJson(jsonString);

import 'dart:convert';

WorkFlowModelResponseModel workFlowModelResponseModelFromJson(String str) => WorkFlowModelResponseModel.fromJson(json.decode(str));

String workFlowModelResponseModelToJson(WorkFlowModelResponseModel data) => json.encode(data.toJson());

class WorkFlowModelResponseModel {
    bool? success;
    List<String>? messages;
    ParsedData? parsedData;
    dynamic validationErrors;
    ParsedGos? parsedGos;
    ParsedLos? parsedLos;

    WorkFlowModelResponseModel({
        this.success,
        this.messages,
        this.parsedData,
        this.validationErrors,
        this.parsedGos,
        this.parsedLos,
    });

    WorkFlowModelResponseModel copyWith({
        bool? success,
        List<String>? messages,
        ParsedData? parsedData,
        dynamic validationErrors,
        ParsedGos? parsedGos,
        ParsedLos? parsedLos,
    }) => 
        WorkFlowModelResponseModel(
            success: success ?? this.success,
            messages: messages ?? this.messages,
            parsedData: parsedData ?? this.parsedData,
            validationErrors: validationErrors ?? this.validationErrors,
            parsedGos: parsedGos ?? this.parsedGos,
            parsedLos: parsedLos ?? this.parsedLos,
        );

    factory WorkFlowModelResponseModel.fromJson(Map<String, dynamic> json) => WorkFlowModelResponseModel(
        success: json["success"],
        messages: json["messages"] == null ? [] : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null ? null : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"],
        parsedGos: json["parsed_gos"] == null ? null : ParsedGos.fromJson(json["parsed_gos"]),
        parsedLos: json["parsed_los"] == null ? null : ParsedLos.fromJson(json["parsed_los"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "messages": messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors,
        "parsed_gos": parsedGos?.toJson(),
        "parsed_los": parsedLos?.toJson(),
    };
}

class ParsedData {
    Go? go;
    ParsedLos? localObjectives;

    ParsedData({
        this.go,
        this.localObjectives,
    });

    ParsedData copyWith({
        Go? go,
        ParsedLos? localObjectives,
    }) => 
        ParsedData(
            go: go ?? this.go,
            localObjectives: localObjectives ?? this.localObjectives,
        );

    factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        go: json["GO"] == null ? null : Go.fromJson(json["GO"]),
        localObjectives: json["local_objectives"] == null ? null : ParsedLos.fromJson(json["local_objectives"]),
    );

    Map<String, dynamic> toJson() => {
        "GO": go?.toJson(),
        "local_objectives": localObjectives?.toJson(),
    };
}

class Go {
    GlobalObjectives? globalObjectives;
    ProcessOwnership? processOwnership;
    TriggerDefinition? triggerDefinition;
    List<LocalObjectivesList>? localObjectivesList;
    List<PathwayDefinition>? pathwayDefinitions;
    List<BusinessRule>? businessRules;
    PerformanceMetadata? performanceMetadata;
    ProcessMiningSchema? processMiningSchema;
    PerformanceDiscoveryMetrics? performanceDiscoveryMetrics;
    ConformanceAnalytics? conformanceAnalytics;
    AdvancedProcessIntelligence? advancedProcessIntelligence;
    RollbackAnalytics? rollbackAnalytics;
    List<RollbackPathway>? rollbackPathways;
    ValidationRules? validationRules;
    List<DataConstraint>? dataConstraints;
    List<ProcessFlow>? processFlow;

    Go({
        this.globalObjectives,
        this.processOwnership,
        this.triggerDefinition,
        this.localObjectivesList,
        this.pathwayDefinitions,
        this.businessRules,
        this.performanceMetadata,
        this.processMiningSchema,
        this.performanceDiscoveryMetrics,
        this.conformanceAnalytics,
        this.advancedProcessIntelligence,
        this.rollbackAnalytics,
        this.rollbackPathways,
        this.validationRules,
        this.dataConstraints,
        this.processFlow,
    });

    Go copyWith({
        GlobalObjectives? globalObjectives,
        ProcessOwnership? processOwnership,
        TriggerDefinition? triggerDefinition,
        List<LocalObjectivesList>? localObjectivesList,
        List<PathwayDefinition>? pathwayDefinitions,
        List<BusinessRule>? businessRules,
        PerformanceMetadata? performanceMetadata,
        ProcessMiningSchema? processMiningSchema,
        PerformanceDiscoveryMetrics? performanceDiscoveryMetrics,
        ConformanceAnalytics? conformanceAnalytics,
        AdvancedProcessIntelligence? advancedProcessIntelligence,
        RollbackAnalytics? rollbackAnalytics,
        List<RollbackPathway>? rollbackPathways,
        ValidationRules? validationRules,
        List<DataConstraint>? dataConstraints,
        List<ProcessFlow>? processFlow,
    }) => 
        Go(
            globalObjectives: globalObjectives ?? this.globalObjectives,
            processOwnership: processOwnership ?? this.processOwnership,
            triggerDefinition: triggerDefinition ?? this.triggerDefinition,
            localObjectivesList: localObjectivesList ?? this.localObjectivesList,
            pathwayDefinitions: pathwayDefinitions ?? this.pathwayDefinitions,
            businessRules: businessRules ?? this.businessRules,
            performanceMetadata: performanceMetadata ?? this.performanceMetadata,
            processMiningSchema: processMiningSchema ?? this.processMiningSchema,
            performanceDiscoveryMetrics: performanceDiscoveryMetrics ?? this.performanceDiscoveryMetrics,
            conformanceAnalytics: conformanceAnalytics ?? this.conformanceAnalytics,
            advancedProcessIntelligence: advancedProcessIntelligence ?? this.advancedProcessIntelligence,
            rollbackAnalytics: rollbackAnalytics ?? this.rollbackAnalytics,
            rollbackPathways: rollbackPathways ?? this.rollbackPathways,
            validationRules: validationRules ?? this.validationRules,
            dataConstraints: dataConstraints ?? this.dataConstraints,
            processFlow: processFlow ?? this.processFlow,
        );

    factory Go.fromJson(Map<String, dynamic> json) => Go(
        globalObjectives: json["global_objectives"] == null ? null : GlobalObjectives.fromJson(json["global_objectives"]),
        processOwnership: json["process_ownership"] == null ? null : ProcessOwnership.fromJson(json["process_ownership"]),
        triggerDefinition: json["trigger_definition"] == null ? null : TriggerDefinition.fromJson(json["trigger_definition"]),
        localObjectivesList: json["local_objectives_list"] == null ? [] : List<LocalObjectivesList>.from(json["local_objectives_list"]!.map((x) => LocalObjectivesList.fromJson(x))),
        pathwayDefinitions: json["pathway_definitions"] == null ? [] : List<PathwayDefinition>.from(json["pathway_definitions"]!.map((x) => PathwayDefinition.fromJson(x))),
        businessRules: json["business_rules"] == null ? [] : List<BusinessRule>.from(json["business_rules"]!.map((x) => BusinessRule.fromJson(x))),
        performanceMetadata: json["performance_metadata"] == null ? null : PerformanceMetadata.fromJson(json["performance_metadata"]),
        processMiningSchema: json["process_mining_schema"] == null ? null : ProcessMiningSchema.fromJson(json["process_mining_schema"]),
        performanceDiscoveryMetrics: json["performance_discovery_metrics"] == null ? null : PerformanceDiscoveryMetrics.fromJson(json["performance_discovery_metrics"]),
        conformanceAnalytics: json["conformance_analytics"] == null ? null : ConformanceAnalytics.fromJson(json["conformance_analytics"]),
        advancedProcessIntelligence: json["advanced_process_intelligence"] == null ? null : AdvancedProcessIntelligence.fromJson(json["advanced_process_intelligence"]),
        rollbackAnalytics: json["rollback_analytics"] == null ? null : RollbackAnalytics.fromJson(json["rollback_analytics"]),
        rollbackPathways: json["rollback_pathways"] == null ? [] : List<RollbackPathway>.from(json["rollback_pathways"]!.map((x) => RollbackPathway.fromJson(x))),
        validationRules: json["validation_rules"] == null ? null : ValidationRules.fromJson(json["validation_rules"]),
        dataConstraints: json["data_constraints"] == null ? [] : List<DataConstraint>.from(json["data_constraints"]!.map((x) => DataConstraint.fromJson(x))),
        processFlow: json["process_flow"] == null ? [] : List<ProcessFlow>.from(json["process_flow"]!.map((x) => ProcessFlow.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "global_objectives": globalObjectives?.toJson(),
        "process_ownership": processOwnership?.toJson(),
        "trigger_definition": triggerDefinition?.toJson(),
        "local_objectives_list": localObjectivesList == null ? [] : List<dynamic>.from(localObjectivesList!.map((x) => x.toJson())),
        "pathway_definitions": pathwayDefinitions == null ? [] : List<dynamic>.from(pathwayDefinitions!.map((x) => x.toJson())),
        "business_rules": businessRules == null ? [] : List<dynamic>.from(businessRules!.map((x) => x.toJson())),
        "performance_metadata": performanceMetadata?.toJson(),
        "process_mining_schema": processMiningSchema?.toJson(),
        "performance_discovery_metrics": performanceDiscoveryMetrics?.toJson(),
        "conformance_analytics": conformanceAnalytics?.toJson(),
        "advanced_process_intelligence": advancedProcessIntelligence?.toJson(),
        "rollback_analytics": rollbackAnalytics?.toJson(),
        "rollback_pathways": rollbackPathways == null ? [] : List<dynamic>.from(rollbackPathways!.map((x) => x.toJson())),
        "validation_rules": validationRules?.toJson(),
        "data_constraints": dataConstraints == null ? [] : List<dynamic>.from(dataConstraints!.map((x) => x.toJson())),
        "process_flow": processFlow == null ? [] : List<dynamic>.from(processFlow!.map((x) => x.toJson())),
    };
}

class AdvancedProcessIntelligence {
    String? id;
    String? goId;
    ProcessHealthScore? processHealthScore;
    PredictionModels? predictionModels;
    OptimizationInsights? optimizationInsights;

    AdvancedProcessIntelligence({
        this.id,
        this.goId,
        this.processHealthScore,
        this.predictionModels,
        this.optimizationInsights,
    });

    AdvancedProcessIntelligence copyWith({
        String? id,
        String? goId,
        ProcessHealthScore? processHealthScore,
        PredictionModels? predictionModels,
        OptimizationInsights? optimizationInsights,
    }) => 
        AdvancedProcessIntelligence(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            processHealthScore: processHealthScore ?? this.processHealthScore,
            predictionModels: predictionModels ?? this.predictionModels,
            optimizationInsights: optimizationInsights ?? this.optimizationInsights,
        );

    factory AdvancedProcessIntelligence.fromJson(Map<String, dynamic> json) => AdvancedProcessIntelligence(
        id: json["id"],
        goId: json["go_id"],
        processHealthScore: json["process_health_score"] == null ? null : ProcessHealthScore.fromJson(json["process_health_score"]),
        predictionModels: json["prediction_models"] == null ? null : PredictionModels.fromJson(json["prediction_models"]),
        optimizationInsights: json["optimization_insights"] == null ? null : OptimizationInsights.fromJson(json["optimization_insights"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "process_health_score": processHealthScore?.toJson(),
        "prediction_models": predictionModels?.toJson(),
        "optimization_insights": optimizationInsights?.toJson(),
    };
}

class OptimizationInsights {
    List<String>? bottleneckElimination;
    List<String>? resourceReallocation;
    List<String>? pathwayOptimization;

    OptimizationInsights({
        this.bottleneckElimination,
        this.resourceReallocation,
        this.pathwayOptimization,
    });

    OptimizationInsights copyWith({
        List<String>? bottleneckElimination,
        List<String>? resourceReallocation,
        List<String>? pathwayOptimization,
    }) => 
        OptimizationInsights(
            bottleneckElimination: bottleneckElimination ?? this.bottleneckElimination,
            resourceReallocation: resourceReallocation ?? this.resourceReallocation,
            pathwayOptimization: pathwayOptimization ?? this.pathwayOptimization,
        );

    factory OptimizationInsights.fromJson(Map<String, dynamic> json) => OptimizationInsights(
        bottleneckElimination: json["bottleneck_elimination"] == null ? [] : List<String>.from(json["bottleneck_elimination"]!.map((x) => x)),
        resourceReallocation: json["resource_reallocation"] == null ? [] : List<String>.from(json["resource_reallocation"]!.map((x) => x)),
        pathwayOptimization: json["pathway_optimization"] == null ? [] : List<String>.from(json["pathway_optimization"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "bottleneck_elimination": bottleneckElimination == null ? [] : List<dynamic>.from(bottleneckElimination!.map((x) => x)),
        "resource_reallocation": resourceReallocation == null ? [] : List<dynamic>.from(resourceReallocation!.map((x) => x)),
        "pathway_optimization": pathwayOptimization == null ? [] : List<dynamic>.from(pathwayOptimization!.map((x) => x)),
    };
}

class PredictionModels {
    CompletionTimeForecast? completionTimeForecast;
    FailurePrediction? failurePrediction;

    PredictionModels({
        this.completionTimeForecast,
        this.failurePrediction,
    });

    PredictionModels copyWith({
        CompletionTimeForecast? completionTimeForecast,
        FailurePrediction? failurePrediction,
    }) => 
        PredictionModels(
            completionTimeForecast: completionTimeForecast ?? this.completionTimeForecast,
            failurePrediction: failurePrediction ?? this.failurePrediction,
        );

    factory PredictionModels.fromJson(Map<String, dynamic> json) => PredictionModels(
        completionTimeForecast: json["completion_time_forecast"] == null ? null : CompletionTimeForecast.fromJson(json["completion_time_forecast"]),
        failurePrediction: json["failure_prediction"] == null ? null : FailurePrediction.fromJson(json["failure_prediction"]),
    );

    Map<String, dynamic> toJson() => {
        "completion_time_forecast": completionTimeForecast?.toJson(),
        "failure_prediction": failurePrediction?.toJson(),
    };
}

class CompletionTimeForecast {
    String? algorithm;
    int? accuracy;
    String? confidenceInterval;

    CompletionTimeForecast({
        this.algorithm,
        this.accuracy,
        this.confidenceInterval,
    });

    CompletionTimeForecast copyWith({
        String? algorithm,
        int? accuracy,
        String? confidenceInterval,
    }) => 
        CompletionTimeForecast(
            algorithm: algorithm ?? this.algorithm,
            accuracy: accuracy ?? this.accuracy,
            confidenceInterval: confidenceInterval ?? this.confidenceInterval,
        );

    factory CompletionTimeForecast.fromJson(Map<String, dynamic> json) => CompletionTimeForecast(
        algorithm: json["algorithm"],
        accuracy: json["accuracy"],
        confidenceInterval: json["confidence_interval"],
    );

    Map<String, dynamic> toJson() => {
        "algorithm": algorithm,
        "accuracy": accuracy,
        "confidence_interval": confidenceInterval,
    };
}

class FailurePrediction {
    String? algorithm;
    int? precision;
    int? recall;

    FailurePrediction({
        this.algorithm,
        this.precision,
        this.recall,
    });

    FailurePrediction copyWith({
        String? algorithm,
        int? precision,
        int? recall,
    }) => 
        FailurePrediction(
            algorithm: algorithm ?? this.algorithm,
            precision: precision ?? this.precision,
            recall: recall ?? this.recall,
        );

    factory FailurePrediction.fromJson(Map<String, dynamic> json) => FailurePrediction(
        algorithm: json["algorithm"],
        precision: json["precision"],
        recall: json["recall"],
    );

    Map<String, dynamic> toJson() => {
        "algorithm": algorithm,
        "precision": precision,
        "recall": recall,
    };
}

class ProcessHealthScore {
    int? performanceScore;
    int? complianceScore;
    int? efficiencyScore;
    int? overallHealth;

    ProcessHealthScore({
        this.performanceScore,
        this.complianceScore,
        this.efficiencyScore,
        this.overallHealth,
    });

    ProcessHealthScore copyWith({
        int? performanceScore,
        int? complianceScore,
        int? efficiencyScore,
        int? overallHealth,
    }) => 
        ProcessHealthScore(
            performanceScore: performanceScore ?? this.performanceScore,
            complianceScore: complianceScore ?? this.complianceScore,
            efficiencyScore: efficiencyScore ?? this.efficiencyScore,
            overallHealth: overallHealth ?? this.overallHealth,
        );

    factory ProcessHealthScore.fromJson(Map<String, dynamic> json) => ProcessHealthScore(
        performanceScore: json["performance_score"],
        complianceScore: json["compliance_score"],
        efficiencyScore: json["efficiency_score"],
        overallHealth: json["overall_health"],
    );

    Map<String, dynamic> toJson() => {
        "performance_score": performanceScore,
        "compliance_score": complianceScore,
        "efficiency_score": efficiencyScore,
        "overall_health": overallHealth,
    };
}

class BusinessRule {
    String? id;
    String? goId;
    String? ruleName;
    String? ruleDescription;
    List<String>? ruleInputs;
    String? ruleOperation;
    List<String>? ruleOutputs;
    String? ruleErrorMessage;
    String? ruleValidationType;

    BusinessRule({
        this.id,
        this.goId,
        this.ruleName,
        this.ruleDescription,
        this.ruleInputs,
        this.ruleOperation,
        this.ruleOutputs,
        this.ruleErrorMessage,
        this.ruleValidationType,
    });

    BusinessRule copyWith({
        String? id,
        String? goId,
        String? ruleName,
        String? ruleDescription,
        List<String>? ruleInputs,
        String? ruleOperation,
        List<String>? ruleOutputs,
        String? ruleErrorMessage,
        String? ruleValidationType,
    }) => 
        BusinessRule(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            ruleName: ruleName ?? this.ruleName,
            ruleDescription: ruleDescription ?? this.ruleDescription,
            ruleInputs: ruleInputs ?? this.ruleInputs,
            ruleOperation: ruleOperation ?? this.ruleOperation,
            ruleOutputs: ruleOutputs ?? this.ruleOutputs,
            ruleErrorMessage: ruleErrorMessage ?? this.ruleErrorMessage,
            ruleValidationType: ruleValidationType ?? this.ruleValidationType,
        );

    factory BusinessRule.fromJson(Map<String, dynamic> json) => BusinessRule(
        id: json["id"],
        goId: json["go_id"],
        ruleName: json["rule_name"],
        ruleDescription: json["rule_description"],
        ruleInputs: json["rule_inputs"] == null ? [] : List<String>.from(json["rule_inputs"]!.map((x) => x)),
        ruleOperation: json["rule_operation"],
        ruleOutputs: json["rule_outputs"] == null ? [] : List<String>.from(json["rule_outputs"]!.map((x) => x)),
        ruleErrorMessage: json["rule_error_message"],
        ruleValidationType: json["rule_validation_type"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "rule_name": ruleName,
        "rule_description": ruleDescription,
        "rule_inputs": ruleInputs == null ? [] : List<dynamic>.from(ruleInputs!.map((x) => x)),
        "rule_operation": ruleOperation,
        "rule_outputs": ruleOutputs == null ? [] : List<dynamic>.from(ruleOutputs!.map((x) => x)),
        "rule_error_message": ruleErrorMessage,
        "rule_validation_type": ruleValidationType,
    };
}

class ConformanceAnalytics {
    String? id;
    String? goId;
    int? complianceRate;
    ExecutionVariance? executionVariance;
    ExceptionPatterns? exceptionPatterns;

    ConformanceAnalytics({
        this.id,
        this.goId,
        this.complianceRate,
        this.executionVariance,
        this.exceptionPatterns,
    });

    ConformanceAnalytics copyWith({
        String? id,
        String? goId,
        int? complianceRate,
        ExecutionVariance? executionVariance,
        ExceptionPatterns? exceptionPatterns,
    }) => 
        ConformanceAnalytics(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            complianceRate: complianceRate ?? this.complianceRate,
            executionVariance: executionVariance ?? this.executionVariance,
            exceptionPatterns: exceptionPatterns ?? this.exceptionPatterns,
        );

    factory ConformanceAnalytics.fromJson(Map<String, dynamic> json) => ConformanceAnalytics(
        id: json["id"],
        goId: json["go_id"],
        complianceRate: json["compliance_rate"],
        executionVariance: json["execution_variance"] == null ? null : ExecutionVariance.fromJson(json["execution_variance"]),
        exceptionPatterns: json["exception_patterns"] == null ? null : ExceptionPatterns.fromJson(json["exception_patterns"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "compliance_rate": complianceRate,
        "execution_variance": executionVariance?.toJson(),
        "exception_patterns": exceptionPatterns?.toJson(),
    };
}

class ExceptionPatterns {
    InputTimeout? inputTimeout;
    ValidationFailure? validationFailure;
    ExceptionPatternsSystemError? systemError;

    ExceptionPatterns({
        this.inputTimeout,
        this.validationFailure,
        this.systemError,
    });

    ExceptionPatterns copyWith({
        InputTimeout? inputTimeout,
        ValidationFailure? validationFailure,
        ExceptionPatternsSystemError? systemError,
    }) => 
        ExceptionPatterns(
            inputTimeout: inputTimeout ?? this.inputTimeout,
            validationFailure: validationFailure ?? this.validationFailure,
            systemError: systemError ?? this.systemError,
        );

    factory ExceptionPatterns.fromJson(Map<String, dynamic> json) => ExceptionPatterns(
        inputTimeout: json["input_timeout"] == null ? null : InputTimeout.fromJson(json["input_timeout"]),
        validationFailure: json["validation_failure"] == null ? null : ValidationFailure.fromJson(json["validation_failure"]),
        systemError: json["system_error"] == null ? null : ExceptionPatternsSystemError.fromJson(json["system_error"]),
    );

    Map<String, dynamic> toJson() => {
        "input_timeout": inputTimeout?.toJson(),
        "validation_failure": validationFailure?.toJson(),
        "system_error": systemError?.toJson(),
    };
}

class InputTimeout {
    int? frequency;
    List<String>? affectedPathways;
    int? recoverySuccessRate;

    InputTimeout({
        this.frequency,
        this.affectedPathways,
        this.recoverySuccessRate,
    });

    InputTimeout copyWith({
        int? frequency,
        List<String>? affectedPathways,
        int? recoverySuccessRate,
    }) => 
        InputTimeout(
            frequency: frequency ?? this.frequency,
            affectedPathways: affectedPathways ?? this.affectedPathways,
            recoverySuccessRate: recoverySuccessRate ?? this.recoverySuccessRate,
        );

    factory InputTimeout.fromJson(Map<String, dynamic> json) => InputTimeout(
        frequency: json["frequency"],
        affectedPathways: json["affected_pathways"] == null ? [] : List<String>.from(json["affected_pathways"]!.map((x) => x)),
        recoverySuccessRate: json["recovery_success_rate"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "affected_pathways": affectedPathways == null ? [] : List<dynamic>.from(affectedPathways!.map((x) => x)),
        "recovery_success_rate": recoverySuccessRate,
    };
}

class ExceptionPatternsSystemError {
    int? frequency;
    List<String>? errorCategories;
    int? automaticRecoveryRate;

    ExceptionPatternsSystemError({
        this.frequency,
        this.errorCategories,
        this.automaticRecoveryRate,
    });

    ExceptionPatternsSystemError copyWith({
        int? frequency,
        List<String>? errorCategories,
        int? automaticRecoveryRate,
    }) => 
        ExceptionPatternsSystemError(
            frequency: frequency ?? this.frequency,
            errorCategories: errorCategories ?? this.errorCategories,
            automaticRecoveryRate: automaticRecoveryRate ?? this.automaticRecoveryRate,
        );

    factory ExceptionPatternsSystemError.fromJson(Map<String, dynamic> json) => ExceptionPatternsSystemError(
        frequency: json["frequency"],
        errorCategories: json["error_categories"] == null ? [] : List<String>.from(json["error_categories"]!.map((x) => x)),
        automaticRecoveryRate: json["automatic_recovery_rate"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "error_categories": errorCategories == null ? [] : List<dynamic>.from(errorCategories!.map((x) => x)),
        "automatic_recovery_rate": automaticRecoveryRate,
    };
}

class ValidationFailure {
    int? frequency;
    List<String>? mostCommonFailures;
    String? resolutionTime;

    ValidationFailure({
        this.frequency,
        this.mostCommonFailures,
        this.resolutionTime,
    });

    ValidationFailure copyWith({
        int? frequency,
        List<String>? mostCommonFailures,
        String? resolutionTime,
    }) => 
        ValidationFailure(
            frequency: frequency ?? this.frequency,
            mostCommonFailures: mostCommonFailures ?? this.mostCommonFailures,
            resolutionTime: resolutionTime ?? this.resolutionTime,
        );

    factory ValidationFailure.fromJson(Map<String, dynamic> json) => ValidationFailure(
        frequency: json["frequency"],
        mostCommonFailures: json["most_common_failures"] == null ? [] : List<String>.from(json["most_common_failures"]!.map((x) => x)),
        resolutionTime: json["resolution_time"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "most_common_failures": mostCommonFailures == null ? [] : List<dynamic>.from(mostCommonFailures!.map((x) => x)),
        "resolution_time": resolutionTime,
    };
}

class ExecutionVariance {
    NotifyEmployee? reviewLeaveRequest;
    NotifyEmployee? notifyEmployee;

    ExecutionVariance({
        this.reviewLeaveRequest,
        this.notifyEmployee,
    });

    ExecutionVariance copyWith({
        NotifyEmployee? reviewLeaveRequest,
        NotifyEmployee? notifyEmployee,
    }) => 
        ExecutionVariance(
            reviewLeaveRequest: reviewLeaveRequest ?? this.reviewLeaveRequest,
            notifyEmployee: notifyEmployee ?? this.notifyEmployee,
        );

    factory ExecutionVariance.fromJson(Map<String, dynamic> json) => ExecutionVariance(
        reviewLeaveRequest: json["ReviewLeaveRequest"] == null ? null : NotifyEmployee.fromJson(json["ReviewLeaveRequest"]),
        notifyEmployee: json["NotifyEmployee"] == null ? null : NotifyEmployee.fromJson(json["NotifyEmployee"]),
    );

    Map<String, dynamic> toJson() => {
        "ReviewLeaveRequest": reviewLeaveRequest?.toJson(),
        "NotifyEmployee": notifyEmployee?.toJson(),
    };
}

class NotifyEmployee {
    String? expectedDuration;
    String? actualDurationRange;
    List<String>? varianceCauses;

    NotifyEmployee({
        this.expectedDuration,
        this.actualDurationRange,
        this.varianceCauses,
    });

    NotifyEmployee copyWith({
        String? expectedDuration,
        String? actualDurationRange,
        List<String>? varianceCauses,
    }) => 
        NotifyEmployee(
            expectedDuration: expectedDuration ?? this.expectedDuration,
            actualDurationRange: actualDurationRange ?? this.actualDurationRange,
            varianceCauses: varianceCauses ?? this.varianceCauses,
        );

    factory NotifyEmployee.fromJson(Map<String, dynamic> json) => NotifyEmployee(
        expectedDuration: json["expected_duration"],
        actualDurationRange: json["actual_duration_range"],
        varianceCauses: json["variance_causes"] == null ? [] : List<String>.from(json["variance_causes"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "expected_duration": expectedDuration,
        "actual_duration_range": actualDurationRange,
        "variance_causes": varianceCauses == null ? [] : List<dynamic>.from(varianceCauses!.map((x) => x)),
    };
}

class DataConstraint {
    String? id;
    String? goId;
    String? entity;
    String? attribute;
    String? dataType;
    String? constraintText;
    String? errorMessage;

    DataConstraint({
        this.id,
        this.goId,
        this.entity,
        this.attribute,
        this.dataType,
        this.constraintText,
        this.errorMessage,
    });

    DataConstraint copyWith({
        String? id,
        String? goId,
        String? entity,
        String? attribute,
        String? dataType,
        String? constraintText,
        String? errorMessage,
    }) => 
        DataConstraint(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            entity: entity ?? this.entity,
            attribute: attribute ?? this.attribute,
            dataType: dataType ?? this.dataType,
            constraintText: constraintText ?? this.constraintText,
            errorMessage: errorMessage ?? this.errorMessage,
        );

    factory DataConstraint.fromJson(Map<String, dynamic> json) => DataConstraint(
        id: json["id"],
        goId: json["go_id"],
        entity: json["entity"],
        attribute: json["attribute"],
        dataType: json["data_type"],
        constraintText: json["constraint_text"],
        errorMessage: json["error_message"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "entity": entity,
        "attribute": attribute,
        "data_type": dataType,
        "constraint_text": constraintText,
        "error_message": errorMessage,
    };
}

class GlobalObjectives {
    String? name;
    String? version;
    String? status;
    String? description;
    String? primaryEntity;
    String? classification;
    String? tenantId;
    String? tenantName;
    String? bookId;
    String? bookName;
    String? chapterId;
    String? chapterName;
    String? versionType;
    String? goId;

    GlobalObjectives({
        this.name,
        this.version,
        this.status,
        this.description,
        this.primaryEntity,
        this.classification,
        this.tenantId,
        this.tenantName,
        this.bookId,
        this.bookName,
        this.chapterId,
        this.chapterName,
        this.versionType,
        this.goId,
    });

    GlobalObjectives copyWith({
        String? name,
        String? version,
        String? status,
        String? description,
        String? primaryEntity,
        String? classification,
        String? tenantId,
        String? tenantName,
        String? bookId,
        String? bookName,
        String? chapterId,
        String? chapterName,
        String? versionType,
        String? goId,
    }) => 
        GlobalObjectives(
            name: name ?? this.name,
            version: version ?? this.version,
            status: status ?? this.status,
            description: description ?? this.description,
            primaryEntity: primaryEntity ?? this.primaryEntity,
            classification: classification ?? this.classification,
            tenantId: tenantId ?? this.tenantId,
            tenantName: tenantName ?? this.tenantName,
            bookId: bookId ?? this.bookId,
            bookName: bookName ?? this.bookName,
            chapterId: chapterId ?? this.chapterId,
            chapterName: chapterName ?? this.chapterName,
            versionType: versionType ?? this.versionType,
            goId: goId ?? this.goId,
        );

    factory GlobalObjectives.fromJson(Map<String, dynamic> json) => GlobalObjectives(
        name: json["name"],
        version: json["version"],
        status: json["status"],
        description: json["description"],
        primaryEntity: json["primary_entity"],
        classification: json["classification"],
        tenantId: json["tenant_id"],
        tenantName: json["tenant_name"],
        bookId: json["book_id"],
        bookName: json["book_name"],
        chapterId: json["chapter_id"],
        chapterName: json["chapter_name"],
        versionType: json["version_type"],
        goId: json["go_id"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "version": version,
        "status": status,
        "description": description,
        "primary_entity": primaryEntity,
        "classification": classification,
        "tenant_id": tenantId,
        "tenant_name": tenantName,
        "book_id": bookId,
        "book_name": bookName,
        "chapter_id": chapterId,
        "chapter_name": chapterName,
        "version_type": versionType,
        "go_id": goId,
    };
}

class LocalObjectivesList {
    int? loNumber;
    String? loName;
    String? actorType;
    String? id;
    String? workSource;
    bool? terminal;

    LocalObjectivesList({
        this.loNumber,
        this.loName,
        this.actorType,
        this.id,
        this.workSource,
        this.terminal,
    });

    LocalObjectivesList copyWith({
        int? loNumber,
        String? loName,
        String? actorType,
        String? id,
        String? workSource,
        bool? terminal,
    }) => 
        LocalObjectivesList(
            loNumber: loNumber ?? this.loNumber,
            loName: loName ?? this.loName,
            actorType: actorType ?? this.actorType,
            id: id ?? this.id,
            workSource: workSource ?? this.workSource,
            terminal: terminal ?? this.terminal,
        );

    factory LocalObjectivesList.fromJson(Map<String, dynamic> json) => LocalObjectivesList(
        loNumber: json["lo_number"],
        loName: json["lo_name"],
        actorType: json["actor_type"],
        id: json["id"],
        workSource: json["work_source"],
        terminal: json["terminal"],
    );

    Map<String, dynamic> toJson() => {
        "lo_number": loNumber,
        "lo_name": loName,
        "actor_type": actorType,
        "id": id,
        "work_source": workSource,
        "terminal": terminal,
    };
}

class PathwayDefinition {
    String? id;
    int? pathwayNumber;
    String? pathwayName;
    List<String>? steps;

    PathwayDefinition({
        this.id,
        this.pathwayNumber,
        this.pathwayName,
        this.steps,
    });

    PathwayDefinition copyWith({
        String? id,
        int? pathwayNumber,
        String? pathwayName,
        List<String>? steps,
    }) => 
        PathwayDefinition(
            id: id ?? this.id,
            pathwayNumber: pathwayNumber ?? this.pathwayNumber,
            pathwayName: pathwayName ?? this.pathwayName,
            steps: steps ?? this.steps,
        );

    factory PathwayDefinition.fromJson(Map<String, dynamic> json) => PathwayDefinition(
        id: json["id"],
        pathwayNumber: json["pathway_number"],
        pathwayName: json["pathway_name"],
        steps: json["steps"] == null ? [] : List<String>.from(json["steps"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "pathway_number": pathwayNumber,
        "pathway_name": pathwayName,
        "steps": steps == null ? [] : List<dynamic>.from(steps!.map((x) => x)),
    };
}

class PerformanceDiscoveryMetrics {
    String? id;
    String? goId;
    PathwayFrequency? pathwayFrequency;
    BottleneckAnalysis? bottleneckAnalysis;
    ResourcePatterns? resourcePatterns;

    PerformanceDiscoveryMetrics({
        this.id,
        this.goId,
        this.pathwayFrequency,
        this.bottleneckAnalysis,
        this.resourcePatterns,
    });

    PerformanceDiscoveryMetrics copyWith({
        String? id,
        String? goId,
        PathwayFrequency? pathwayFrequency,
        BottleneckAnalysis? bottleneckAnalysis,
        ResourcePatterns? resourcePatterns,
    }) => 
        PerformanceDiscoveryMetrics(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            pathwayFrequency: pathwayFrequency ?? this.pathwayFrequency,
            bottleneckAnalysis: bottleneckAnalysis ?? this.bottleneckAnalysis,
            resourcePatterns: resourcePatterns ?? this.resourcePatterns,
        );

    factory PerformanceDiscoveryMetrics.fromJson(Map<String, dynamic> json) => PerformanceDiscoveryMetrics(
        id: json["id"],
        goId: json["go_id"],
        pathwayFrequency: json["pathway_frequency"] == null ? null : PathwayFrequency.fromJson(json["pathway_frequency"]),
        bottleneckAnalysis: json["bottleneck_analysis"] == null ? null : BottleneckAnalysis.fromJson(json["bottleneck_analysis"]),
        resourcePatterns: json["resource_patterns"] == null ? null : ResourcePatterns.fromJson(json["resource_patterns"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "pathway_frequency": pathwayFrequency?.toJson(),
        "bottleneck_analysis": bottleneckAnalysis?.toJson(),
        "resource_patterns": resourcePatterns?.toJson(),
    };
}

class BottleneckAnalysis {
    ReviewLeaveRequest? reviewLeaveRequest;

    BottleneckAnalysis({
        this.reviewLeaveRequest,
    });

    BottleneckAnalysis copyWith({
        ReviewLeaveRequest? reviewLeaveRequest,
    }) => 
        BottleneckAnalysis(
            reviewLeaveRequest: reviewLeaveRequest ?? this.reviewLeaveRequest,
        );

    factory BottleneckAnalysis.fromJson(Map<String, dynamic> json) => BottleneckAnalysis(
        reviewLeaveRequest: json["ReviewLeaveRequest"] == null ? null : ReviewLeaveRequest.fromJson(json["ReviewLeaveRequest"]),
    );

    Map<String, dynamic> toJson() => {
        "ReviewLeaveRequest": reviewLeaveRequest?.toJson(),
    };
}

class ReviewLeaveRequest {
    String? averageWaitTime;
    int? queueLength;
    int? resourceUtilization;
    int? failureRate;

    ReviewLeaveRequest({
        this.averageWaitTime,
        this.queueLength,
        this.resourceUtilization,
        this.failureRate,
    });

    ReviewLeaveRequest copyWith({
        String? averageWaitTime,
        int? queueLength,
        int? resourceUtilization,
        int? failureRate,
    }) => 
        ReviewLeaveRequest(
            averageWaitTime: averageWaitTime ?? this.averageWaitTime,
            queueLength: queueLength ?? this.queueLength,
            resourceUtilization: resourceUtilization ?? this.resourceUtilization,
            failureRate: failureRate ?? this.failureRate,
        );

    factory ReviewLeaveRequest.fromJson(Map<String, dynamic> json) => ReviewLeaveRequest(
        averageWaitTime: json["average_wait_time"],
        queueLength: json["queue_length"],
        resourceUtilization: json["resource_utilization"],
        failureRate: json["failure_rate"],
    );

    Map<String, dynamic> toJson() => {
        "average_wait_time": averageWaitTime,
        "queue_length": queueLength,
        "resource_utilization": resourceUtilization,
        "failure_rate": failureRate,
    };
}

class PathwayFrequency {
    DocumentationRequired? standardApproval;
    DocumentationRequired? documentationRequired;

    PathwayFrequency({
        this.standardApproval,
        this.documentationRequired,
    });

    PathwayFrequency copyWith({
        DocumentationRequired? standardApproval,
        DocumentationRequired? documentationRequired,
    }) => 
        PathwayFrequency(
            standardApproval: standardApproval ?? this.standardApproval,
            documentationRequired: documentationRequired ?? this.documentationRequired,
        );

    factory PathwayFrequency.fromJson(Map<String, dynamic> json) => PathwayFrequency(
        standardApproval: json["Standard Approval"] == null ? null : DocumentationRequired.fromJson(json["Standard Approval"]),
        documentationRequired: json["Documentation Required"] == null ? null : DocumentationRequired.fromJson(json["Documentation Required"]),
    );

    Map<String, dynamic> toJson() => {
        "Standard Approval": standardApproval?.toJson(),
        "Documentation Required": documentationRequired?.toJson(),
    };
}

class DocumentationRequired {
    int? frequency;
    int? percentage;
    String? averageDuration;
    int? successRate;

    DocumentationRequired({
        this.frequency,
        this.percentage,
        this.averageDuration,
        this.successRate,
    });

    DocumentationRequired copyWith({
        int? frequency,
        int? percentage,
        String? averageDuration,
        int? successRate,
    }) => 
        DocumentationRequired(
            frequency: frequency ?? this.frequency,
            percentage: percentage ?? this.percentage,
            averageDuration: averageDuration ?? this.averageDuration,
            successRate: successRate ?? this.successRate,
        );

    factory DocumentationRequired.fromJson(Map<String, dynamic> json) => DocumentationRequired(
        frequency: json["frequency"],
        percentage: json["percentage"],
        averageDuration: json["average_duration"],
        successRate: json["success_rate"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "percentage": percentage,
        "average_duration": averageDuration,
        "success_rate": successRate,
    };
}

class ResourcePatterns {
    HrManager? hrManager;
    HrManager? notificationSystem;

    ResourcePatterns({
        this.hrManager,
        this.notificationSystem,
    });

    ResourcePatterns copyWith({
        HrManager? hrManager,
        HrManager? notificationSystem,
    }) => 
        ResourcePatterns(
            hrManager: hrManager ?? this.hrManager,
            notificationSystem: notificationSystem ?? this.notificationSystem,
        );

    factory ResourcePatterns.fromJson(Map<String, dynamic> json) => ResourcePatterns(
        hrManager: json["HR Manager"] == null ? null : HrManager.fromJson(json["HR Manager"]),
        notificationSystem: json["Notification System"] == null ? null : HrManager.fromJson(json["Notification System"]),
    );

    Map<String, dynamic> toJson() => {
        "HR Manager": hrManager?.toJson(),
        "Notification System": notificationSystem?.toJson(),
    };
}

class HrManager {
    String? activeHours;
    String? peakLoadPeriods;
    int? concurrentExecutions;

    HrManager({
        this.activeHours,
        this.peakLoadPeriods,
        this.concurrentExecutions,
    });

    HrManager copyWith({
        String? activeHours,
        String? peakLoadPeriods,
        int? concurrentExecutions,
    }) => 
        HrManager(
            activeHours: activeHours ?? this.activeHours,
            peakLoadPeriods: peakLoadPeriods ?? this.peakLoadPeriods,
            concurrentExecutions: concurrentExecutions ?? this.concurrentExecutions,
        );

    factory HrManager.fromJson(Map<String, dynamic> json) => HrManager(
        activeHours: json["active_hours"],
        peakLoadPeriods: json["peak_load_periods"],
        concurrentExecutions: json["concurrent_executions"],
    );

    Map<String, dynamic> toJson() => {
        "active_hours": activeHours,
        "peak_load_periods": peakLoadPeriods,
        "concurrent_executions": concurrentExecutions,
    };
}

class PerformanceMetadata {
    MetadataData? metadataData;
    String? id;
    String? goId;

    PerformanceMetadata({
        this.metadataData,
        this.id,
        this.goId,
    });

    PerformanceMetadata copyWith({
        MetadataData? metadataData,
        String? id,
        String? goId,
    }) => 
        PerformanceMetadata(
            metadataData: metadataData ?? this.metadataData,
            id: id ?? this.id,
            goId: goId ?? this.goId,
        );

    factory PerformanceMetadata.fromJson(Map<String, dynamic> json) => PerformanceMetadata(
        metadataData: json["metadata_data"] == null ? null : MetadataData.fromJson(json["metadata_data"]),
        id: json["id"],
        goId: json["go_id"],
    );

    Map<String, dynamic> toJson() => {
        "metadata_data": metadataData?.toJson(),
        "id": id,
        "go_id": goId,
    };
}

class MetadataData {
    String? cycleTime;
    int? numberOfPathways;
    VolumeMetrics? volumeMetrics;
    SlaThresholds? slaThresholds;
    CriticalLoPerformance? criticalLoPerformance;

    MetadataData({
        this.cycleTime,
        this.numberOfPathways,
        this.volumeMetrics,
        this.slaThresholds,
        this.criticalLoPerformance,
    });

    MetadataData copyWith({
        String? cycleTime,
        int? numberOfPathways,
        VolumeMetrics? volumeMetrics,
        SlaThresholds? slaThresholds,
        CriticalLoPerformance? criticalLoPerformance,
    }) => 
        MetadataData(
            cycleTime: cycleTime ?? this.cycleTime,
            numberOfPathways: numberOfPathways ?? this.numberOfPathways,
            volumeMetrics: volumeMetrics ?? this.volumeMetrics,
            slaThresholds: slaThresholds ?? this.slaThresholds,
            criticalLoPerformance: criticalLoPerformance ?? this.criticalLoPerformance,
        );

    factory MetadataData.fromJson(Map<String, dynamic> json) => MetadataData(
        cycleTime: json["cycle_time"],
        numberOfPathways: json["number_of_pathways"],
        volumeMetrics: json["volume_metrics"] == null ? null : VolumeMetrics.fromJson(json["volume_metrics"]),
        slaThresholds: json["sla_thresholds"] == null ? null : SlaThresholds.fromJson(json["sla_thresholds"]),
        criticalLoPerformance: json["critical_lo_performance"] == null ? null : CriticalLoPerformance.fromJson(json["critical_lo_performance"]),
    );

    Map<String, dynamic> toJson() => {
        "cycle_time": cycleTime,
        "number_of_pathways": numberOfPathways,
        "volume_metrics": volumeMetrics?.toJson(),
        "sla_thresholds": slaThresholds?.toJson(),
        "critical_lo_performance": criticalLoPerformance?.toJson(),
    };
}

class CriticalLoPerformance {
    String? reviewLeaveRequest;
    String? notifyEmployee;
    String? updateCalendar;
    String? updateLeaveBalance;

    CriticalLoPerformance({
        this.reviewLeaveRequest,
        this.notifyEmployee,
        this.updateCalendar,
        this.updateLeaveBalance,
    });

    CriticalLoPerformance copyWith({
        String? reviewLeaveRequest,
        String? notifyEmployee,
        String? updateCalendar,
        String? updateLeaveBalance,
    }) => 
        CriticalLoPerformance(
            reviewLeaveRequest: reviewLeaveRequest ?? this.reviewLeaveRequest,
            notifyEmployee: notifyEmployee ?? this.notifyEmployee,
            updateCalendar: updateCalendar ?? this.updateCalendar,
            updateLeaveBalance: updateLeaveBalance ?? this.updateLeaveBalance,
        );

    factory CriticalLoPerformance.fromJson(Map<String, dynamic> json) => CriticalLoPerformance(
        reviewLeaveRequest: json["ReviewLeaveRequest"],
        notifyEmployee: json["NotifyEmployee"],
        updateCalendar: json["UpdateCalendar"],
        updateLeaveBalance: json["UpdateLeaveBalance"],
    );

    Map<String, dynamic> toJson() => {
        "ReviewLeaveRequest": reviewLeaveRequest,
        "NotifyEmployee": notifyEmployee,
        "UpdateCalendar": updateCalendar,
        "UpdateLeaveBalance": updateLeaveBalance,
    };
}

class SlaThresholds {
    String? managerReview;
    String? notification;
    String? systemProcessing;

    SlaThresholds({
        this.managerReview,
        this.notification,
        this.systemProcessing,
    });

    SlaThresholds copyWith({
        String? managerReview,
        String? notification,
        String? systemProcessing,
    }) => 
        SlaThresholds(
            managerReview: managerReview ?? this.managerReview,
            notification: notification ?? this.notification,
            systemProcessing: systemProcessing ?? this.systemProcessing,
        );

    factory SlaThresholds.fromJson(Map<String, dynamic> json) => SlaThresholds(
        managerReview: json["manager_review"],
        notification: json["notification"],
        systemProcessing: json["system_processing"],
    );

    Map<String, dynamic> toJson() => {
        "manager_review": managerReview,
        "notification": notification,
        "system_processing": systemProcessing,
    };
}

class VolumeMetrics {
    int? averageVolume;
    int? peakVolume;
    String? unit;

    VolumeMetrics({
        this.averageVolume,
        this.peakVolume,
        this.unit,
    });

    VolumeMetrics copyWith({
        int? averageVolume,
        int? peakVolume,
        String? unit,
    }) => 
        VolumeMetrics(
            averageVolume: averageVolume ?? this.averageVolume,
            peakVolume: peakVolume ?? this.peakVolume,
            unit: unit ?? this.unit,
        );

    factory VolumeMetrics.fromJson(Map<String, dynamic> json) => VolumeMetrics(
        averageVolume: json["average_volume"],
        peakVolume: json["peak_volume"],
        unit: json["unit"],
    );

    Map<String, dynamic> toJson() => {
        "average_volume": averageVolume,
        "peak_volume": peakVolume,
        "unit": unit,
    };
}

class ProcessFlow {
    String? loName;
    String? actorType;
    String? description;
    String? routeType;
    String? id;
    String? goId;
    String? loId;
    List<Condition>? conditions;
    List<String>? routes;
    List<ParallelRoute>? parallelRoutes;
    String? joinAt;

    ProcessFlow({
        this.loName,
        this.actorType,
        this.description,
        this.routeType,
        this.id,
        this.goId,
        this.loId,
        this.conditions,
        this.routes,
        this.parallelRoutes,
        this.joinAt,
    });

    ProcessFlow copyWith({
        String? loName,
        String? actorType,
        String? description,
        String? routeType,
        String? id,
        String? goId,
        String? loId,
        List<Condition>? conditions,
        List<String>? routes,
        List<ParallelRoute>? parallelRoutes,
        String? joinAt,
    }) => 
        ProcessFlow(
            loName: loName ?? this.loName,
            actorType: actorType ?? this.actorType,
            description: description ?? this.description,
            routeType: routeType ?? this.routeType,
            id: id ?? this.id,
            goId: goId ?? this.goId,
            loId: loId ?? this.loId,
            conditions: conditions ?? this.conditions,
            routes: routes ?? this.routes,
            parallelRoutes: parallelRoutes ?? this.parallelRoutes,
            joinAt: joinAt ?? this.joinAt,
        );

    factory ProcessFlow.fromJson(Map<String, dynamic> json) => ProcessFlow(
        loName: json["lo_name"],
        actorType: json["actor_type"],
        description: json["description"],
        routeType: json["route_type"],
        id: json["id"],
        goId: json["go_id"],
        loId: json["lo_id"],
        conditions: json["conditions"] == null ? [] : List<Condition>.from(json["conditions"]!.map((x) => Condition.fromJson(x))),
        routes: json["routes"] == null ? [] : List<String>.from(json["routes"]!.map((x) => x)),
        parallelRoutes: json["parallel_routes"] == null ? [] : List<ParallelRoute>.from(json["parallel_routes"]!.map((x) => ParallelRoute.fromJson(x))),
        joinAt: json["join_at"],
    );

    Map<String, dynamic> toJson() => {
        "lo_name": loName,
        "actor_type": actorType,
        "description": description,
        "route_type": routeType,
        "id": id,
        "go_id": goId,
        "lo_id": loId,
        "conditions": conditions == null ? [] : List<dynamic>.from(conditions!.map((x) => x.toJson())),
        "routes": routes == null ? [] : List<dynamic>.from(routes!.map((x) => x)),
        "parallel_routes": parallelRoutes == null ? [] : List<dynamic>.from(parallelRoutes!.map((x) => x.toJson())),
        "join_at": joinAt,
    };
}

class Condition {
    String? condition;
    String? routeTo;

    Condition({
        this.condition,
        this.routeTo,
    });

    Condition copyWith({
        String? condition,
        String? routeTo,
    }) => 
        Condition(
            condition: condition ?? this.condition,
            routeTo: routeTo ?? this.routeTo,
        );

    factory Condition.fromJson(Map<String, dynamic> json) => Condition(
        condition: json["condition"],
        routeTo: json["route_to"],
    );

    Map<String, dynamic> toJson() => {
        "condition": condition,
        "route_to": routeTo,
    };
}

class ParallelRoute {
    String? routeTo;
    String? description;

    ParallelRoute({
        this.routeTo,
        this.description,
    });

    ParallelRoute copyWith({
        String? routeTo,
        String? description,
    }) => 
        ParallelRoute(
            routeTo: routeTo ?? this.routeTo,
            description: description ?? this.description,
        );

    factory ParallelRoute.fromJson(Map<String, dynamic> json) => ParallelRoute(
        routeTo: json["route_to"],
        description: json["description"],
    );

    Map<String, dynamic> toJson() => {
        "route_to": routeTo,
        "description": description,
    };
}

class ProcessMiningSchema {
    SchemaData? schemaData;
    String? id;
    String? goId;

    ProcessMiningSchema({
        this.schemaData,
        this.id,
        this.goId,
    });

    ProcessMiningSchema copyWith({
        SchemaData? schemaData,
        String? id,
        String? goId,
    }) => 
        ProcessMiningSchema(
            schemaData: schemaData ?? this.schemaData,
            id: id ?? this.id,
            goId: goId ?? this.goId,
        );

    factory ProcessMiningSchema.fromJson(Map<String, dynamic> json) => ProcessMiningSchema(
        schemaData: json["schema_data"] == null ? null : SchemaData.fromJson(json["schema_data"]),
        id: json["id"],
        goId: json["go_id"],
    );

    Map<String, dynamic> toJson() => {
        "schema_data": schemaData?.toJson(),
        "id": id,
        "go_id": goId,
    };
}

class SchemaData {
    EventLogSpecification? eventLogSpecification;

    SchemaData({
        this.eventLogSpecification,
    });

    SchemaData copyWith({
        EventLogSpecification? eventLogSpecification,
    }) => 
        SchemaData(
            eventLogSpecification: eventLogSpecification ?? this.eventLogSpecification,
        );

    factory SchemaData.fromJson(Map<String, dynamic> json) => SchemaData(
        eventLogSpecification: json["event_log_specification"] == null ? null : EventLogSpecification.fromJson(json["event_log_specification"]),
    );

    Map<String, dynamic> toJson() => {
        "event_log_specification": eventLogSpecification?.toJson(),
    };
}

class EventLogSpecification {
    String? caseId;
    String? activity;
    String? eventType;
    String? timestamp;
    String? resource;
    String? duration;
    Attributes? attributes;

    EventLogSpecification({
        this.caseId,
        this.activity,
        this.eventType,
        this.timestamp,
        this.resource,
        this.duration,
        this.attributes,
    });

    EventLogSpecification copyWith({
        String? caseId,
        String? activity,
        String? eventType,
        String? timestamp,
        String? resource,
        String? duration,
        Attributes? attributes,
    }) => 
        EventLogSpecification(
            caseId: caseId ?? this.caseId,
            activity: activity ?? this.activity,
            eventType: eventType ?? this.eventType,
            timestamp: timestamp ?? this.timestamp,
            resource: resource ?? this.resource,
            duration: duration ?? this.duration,
            attributes: attributes ?? this.attributes,
        );

    factory EventLogSpecification.fromJson(Map<String, dynamic> json) => EventLogSpecification(
        caseId: json["case_id"],
        activity: json["activity"],
        eventType: json["event_type"],
        timestamp: json["timestamp"],
        resource: json["resource"],
        duration: json["duration"],
        attributes: json["attributes"] == null ? null : Attributes.fromJson(json["attributes"]),
    );

    Map<String, dynamic> toJson() => {
        "case_id": caseId,
        "activity": activity,
        "event_type": eventType,
        "timestamp": timestamp,
        "resource": resource,
        "duration": duration,
        "attributes": attributes?.toJson(),
    };
}

class Attributes {
    String? entityState;
    String? inputValues;
    String? outputValues;
    String? executionStatus;
    String? errorDetails;

    Attributes({
        this.entityState,
        this.inputValues,
        this.outputValues,
        this.executionStatus,
        this.errorDetails,
    });

    Attributes copyWith({
        String? entityState,
        String? inputValues,
        String? outputValues,
        String? executionStatus,
        String? errorDetails,
    }) => 
        Attributes(
            entityState: entityState ?? this.entityState,
            inputValues: inputValues ?? this.inputValues,
            outputValues: outputValues ?? this.outputValues,
            executionStatus: executionStatus ?? this.executionStatus,
            errorDetails: errorDetails ?? this.errorDetails,
        );

    factory Attributes.fromJson(Map<String, dynamic> json) => Attributes(
        entityState: json["entity_state"],
        inputValues: json["input_values"],
        outputValues: json["output_values"],
        executionStatus: json["execution_status"],
        errorDetails: json["error_details"],
    );

    Map<String, dynamic> toJson() => {
        "entity_state": entityState,
        "input_values": inputValues,
        "output_values": outputValues,
        "execution_status": executionStatus,
        "error_details": errorDetails,
    };
}

class ProcessOwnership {
    String? originator;
    String? processOwner;
    String? businessSponsor;
    String? id;
    String? goId;

    ProcessOwnership({
        this.originator,
        this.processOwner,
        this.businessSponsor,
        this.id,
        this.goId,
    });

    ProcessOwnership copyWith({
        String? originator,
        String? processOwner,
        String? businessSponsor,
        String? id,
        String? goId,
    }) => 
        ProcessOwnership(
            originator: originator ?? this.originator,
            processOwner: processOwner ?? this.processOwner,
            businessSponsor: businessSponsor ?? this.businessSponsor,
            id: id ?? this.id,
            goId: goId ?? this.goId,
        );

    factory ProcessOwnership.fromJson(Map<String, dynamic> json) => ProcessOwnership(
        originator: json["originator"],
        processOwner: json["process_owner"],
        businessSponsor: json["business_sponsor"],
        id: json["id"],
        goId: json["go_id"],
    );

    Map<String, dynamic> toJson() => {
        "originator": originator,
        "process_owner": processOwner,
        "business_sponsor": businessSponsor,
        "id": id,
        "go_id": goId,
    };
}

class RollbackAnalytics {
    String? id;
    String? goId;
    int? rollbackFrequency;
    int? rollbackSuccessRate;
    RollbackTriggers? rollbackTriggers;
    RollbackPathwaysMetrics? rollbackPathwaysMetrics;

    RollbackAnalytics({
        this.id,
        this.goId,
        this.rollbackFrequency,
        this.rollbackSuccessRate,
        this.rollbackTriggers,
        this.rollbackPathwaysMetrics,
    });

    RollbackAnalytics copyWith({
        String? id,
        String? goId,
        int? rollbackFrequency,
        int? rollbackSuccessRate,
        RollbackTriggers? rollbackTriggers,
        RollbackPathwaysMetrics? rollbackPathwaysMetrics,
    }) => 
        RollbackAnalytics(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            rollbackFrequency: rollbackFrequency ?? this.rollbackFrequency,
            rollbackSuccessRate: rollbackSuccessRate ?? this.rollbackSuccessRate,
            rollbackTriggers: rollbackTriggers ?? this.rollbackTriggers,
            rollbackPathwaysMetrics: rollbackPathwaysMetrics ?? this.rollbackPathwaysMetrics,
        );

    factory RollbackAnalytics.fromJson(Map<String, dynamic> json) => RollbackAnalytics(
        id: json["id"],
        goId: json["go_id"],
        rollbackFrequency: json["rollback_frequency"],
        rollbackSuccessRate: json["rollback_success_rate"],
        rollbackTriggers: json["rollback_triggers"] == null ? null : RollbackTriggers.fromJson(json["rollback_triggers"]),
        rollbackPathwaysMetrics: json["rollback_pathways_metrics"] == null ? null : RollbackPathwaysMetrics.fromJson(json["rollback_pathways_metrics"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "rollback_frequency": rollbackFrequency,
        "rollback_success_rate": rollbackSuccessRate,
        "rollback_triggers": rollbackTriggers?.toJson(),
        "rollback_pathways_metrics": rollbackPathwaysMetrics?.toJson(),
    };
}

class RollbackPathwaysMetrics {
    LeaveCancellation? leaveCancellation;
    LeaveCancellation? systemInitiatedRollback;

    RollbackPathwaysMetrics({
        this.leaveCancellation,
        this.systemInitiatedRollback,
    });

    RollbackPathwaysMetrics copyWith({
        LeaveCancellation? leaveCancellation,
        LeaveCancellation? systemInitiatedRollback,
    }) => 
        RollbackPathwaysMetrics(
            leaveCancellation: leaveCancellation ?? this.leaveCancellation,
            systemInitiatedRollback: systemInitiatedRollback ?? this.systemInitiatedRollback,
        );

    factory RollbackPathwaysMetrics.fromJson(Map<String, dynamic> json) => RollbackPathwaysMetrics(
        leaveCancellation: json["leave_cancellation"] == null ? null : LeaveCancellation.fromJson(json["leave_cancellation"]),
        systemInitiatedRollback: json["system_initiated_rollback"] == null ? null : LeaveCancellation.fromJson(json["system_initiated_rollback"]),
    );

    Map<String, dynamic> toJson() => {
        "leave_cancellation": leaveCancellation?.toJson(),
        "system_initiated_rollback": systemInitiatedRollback?.toJson(),
    };
}

class LeaveCancellation {
    int? frequency;
    int? successRate;
    String? averageCompletionTime;

    LeaveCancellation({
        this.frequency,
        this.successRate,
        this.averageCompletionTime,
    });

    LeaveCancellation copyWith({
        int? frequency,
        int? successRate,
        String? averageCompletionTime,
    }) => 
        LeaveCancellation(
            frequency: frequency ?? this.frequency,
            successRate: successRate ?? this.successRate,
            averageCompletionTime: averageCompletionTime ?? this.averageCompletionTime,
        );

    factory LeaveCancellation.fromJson(Map<String, dynamic> json) => LeaveCancellation(
        frequency: json["frequency"],
        successRate: json["success_rate"],
        averageCompletionTime: json["average_completion_time"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "success_rate": successRate,
        "average_completion_time": averageCompletionTime,
    };
}

class RollbackTriggers {
    UserInitiatedClass? userInitiated;
    UserInitiatedClass? systemError;

    RollbackTriggers({
        this.userInitiated,
        this.systemError,
    });

    RollbackTriggers copyWith({
        UserInitiatedClass? userInitiated,
        UserInitiatedClass? systemError,
    }) => 
        RollbackTriggers(
            userInitiated: userInitiated ?? this.userInitiated,
            systemError: systemError ?? this.systemError,
        );

    factory RollbackTriggers.fromJson(Map<String, dynamic> json) => RollbackTriggers(
        userInitiated: json["user_initiated"] == null ? null : UserInitiatedClass.fromJson(json["user_initiated"]),
        systemError: json["system_error"] == null ? null : UserInitiatedClass.fromJson(json["system_error"]),
    );

    Map<String, dynamic> toJson() => {
        "user_initiated": userInitiated?.toJson(),
        "system_error": systemError?.toJson(),
    };
}

class UserInitiatedClass {
    int? frequency;
    int? averageImpactScope;
    String? averageRecoveryTime;

    UserInitiatedClass({
        this.frequency,
        this.averageImpactScope,
        this.averageRecoveryTime,
    });

    UserInitiatedClass copyWith({
        int? frequency,
        int? averageImpactScope,
        String? averageRecoveryTime,
    }) => 
        UserInitiatedClass(
            frequency: frequency ?? this.frequency,
            averageImpactScope: averageImpactScope ?? this.averageImpactScope,
            averageRecoveryTime: averageRecoveryTime ?? this.averageRecoveryTime,
        );

    factory UserInitiatedClass.fromJson(Map<String, dynamic> json) => UserInitiatedClass(
        frequency: json["frequency"],
        averageImpactScope: json["average_impact_scope"],
        averageRecoveryTime: json["average_recovery_time"],
    );

    Map<String, dynamic> toJson() => {
        "frequency": frequency,
        "average_impact_scope": averageImpactScope,
        "average_recovery_time": averageRecoveryTime,
    };
}

class RollbackPathway {
    String? id;
    String? goId;
    String? sourceLo;
    String? targetLo;
    String? pathwayType;

    RollbackPathway({
        this.id,
        this.goId,
        this.sourceLo,
        this.targetLo,
        this.pathwayType,
    });

    RollbackPathway copyWith({
        String? id,
        String? goId,
        String? sourceLo,
        String? targetLo,
        String? pathwayType,
    }) => 
        RollbackPathway(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            sourceLo: sourceLo ?? this.sourceLo,
            targetLo: targetLo ?? this.targetLo,
            pathwayType: pathwayType ?? this.pathwayType,
        );

    factory RollbackPathway.fromJson(Map<String, dynamic> json) => RollbackPathway(
        id: json["id"],
        goId: json["go_id"],
        sourceLo: json["source_lo"],
        targetLo: json["target_lo"],
        pathwayType: json["pathway_type"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "source_lo": sourceLo,
        "target_lo": targetLo,
        "pathway_type": pathwayType,
    };
}

class TriggerDefinition {
    String? triggerType;
    String? triggerCondition;
    String? triggerSchedule;
    List<String>? triggerAttributes;
    String? id;
    String? goId;

    TriggerDefinition({
        this.triggerType,
        this.triggerCondition,
        this.triggerSchedule,
        this.triggerAttributes,
        this.id,
        this.goId,
    });

    TriggerDefinition copyWith({
        String? triggerType,
        String? triggerCondition,
        String? triggerSchedule,
        List<String>? triggerAttributes,
        String? id,
        String? goId,
    }) => 
        TriggerDefinition(
            triggerType: triggerType ?? this.triggerType,
            triggerCondition: triggerCondition ?? this.triggerCondition,
            triggerSchedule: triggerSchedule ?? this.triggerSchedule,
            triggerAttributes: triggerAttributes ?? this.triggerAttributes,
            id: id ?? this.id,
            goId: goId ?? this.goId,
        );

    factory TriggerDefinition.fromJson(Map<String, dynamic> json) => TriggerDefinition(
        triggerType: json["trigger_type"],
        triggerCondition: json["trigger_condition"],
        triggerSchedule: json["trigger_schedule"],
        triggerAttributes: json["trigger_attributes"] == null ? [] : List<String>.from(json["trigger_attributes"]!.map((x) => x)),
        id: json["id"],
        goId: json["go_id"],
    );

    Map<String, dynamic> toJson() => {
        "trigger_type": triggerType,
        "trigger_condition": triggerCondition,
        "trigger_schedule": triggerSchedule,
        "trigger_attributes": triggerAttributes == null ? [] : List<dynamic>.from(triggerAttributes!.map((x) => x)),
        "id": id,
        "go_id": goId,
    };
}

class ValidationRules {
    String? id;
    String? goId;
    List<Rule>? rules;

    ValidationRules({
        this.id,
        this.goId,
        this.rules,
    });

    ValidationRules copyWith({
        String? id,
        String? goId,
        List<Rule>? rules,
    }) => 
        ValidationRules(
            id: id ?? this.id,
            goId: goId ?? this.goId,
            rules: rules ?? this.rules,
        );

    factory ValidationRules.fromJson(Map<String, dynamic> json) => ValidationRules(
        id: json["id"],
        goId: json["go_id"],
        rules: json["rules"] == null ? [] : List<Rule>.from(json["rules"]!.map((x) => Rule.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "go_id": goId,
        "rules": rules == null ? [] : List<dynamic>.from(rules!.map((x) => x.toJson())),
    };
}

class Rule {
    String? id;
    String? ruleName;
    List<String>? ruleInputs;
    String? ruleOperation;
    String? ruleDescription;
    String? ruleOutput;
    String? ruleError;
    String? ruleValidation;

    Rule({
        this.id,
        this.ruleName,
        this.ruleInputs,
        this.ruleOperation,
        this.ruleDescription,
        this.ruleOutput,
        this.ruleError,
        this.ruleValidation,
    });

    Rule copyWith({
        String? id,
        String? ruleName,
        List<String>? ruleInputs,
        String? ruleOperation,
        String? ruleDescription,
        String? ruleOutput,
        String? ruleError,
        String? ruleValidation,
    }) => 
        Rule(
            id: id ?? this.id,
            ruleName: ruleName ?? this.ruleName,
            ruleInputs: ruleInputs ?? this.ruleInputs,
            ruleOperation: ruleOperation ?? this.ruleOperation,
            ruleDescription: ruleDescription ?? this.ruleDescription,
            ruleOutput: ruleOutput ?? this.ruleOutput,
            ruleError: ruleError ?? this.ruleError,
            ruleValidation: ruleValidation ?? this.ruleValidation,
        );

    factory Rule.fromJson(Map<String, dynamic> json) => Rule(
        id: json["id"],
        ruleName: json["rule_name"],
        ruleInputs: json["rule_inputs"] == null ? [] : List<String>.from(json["rule_inputs"]!.map((x) => x)),
        ruleOperation: json["rule_operation"],
        ruleDescription: json["rule_description"],
        ruleOutput: json["rule_output"],
        ruleError: json["rule_error"],
        ruleValidation: json["rule_validation"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "rule_name": ruleName,
        "rule_inputs": ruleInputs == null ? [] : List<dynamic>.from(ruleInputs!.map((x) => x)),
        "rule_operation": ruleOperation,
        "rule_description": ruleDescription,
        "rule_output": ruleOutput,
        "rule_error": ruleError,
        "rule_validation": ruleValidation,
    };
}

class ParsedLos {
    String? name;
    String? version;
    String? status;
    String? goId;
    String? loId;
    String? workflowSource;
    String? functionType;
    String? agentType;
    String? executionRights;

    ParsedLos({
        this.name,
        this.version,
        this.status,
        this.goId,
        this.loId,
        this.workflowSource,
        this.functionType,
        this.agentType,
        this.executionRights,
    });

    ParsedLos copyWith({
        String? name,
        String? version,
        String? status,
        String? goId,
        String? loId,
        String? workflowSource,
        String? functionType,
        String? agentType,
        String? executionRights,
    }) => 
        ParsedLos(
            name: name ?? this.name,
            version: version ?? this.version,
            status: status ?? this.status,
            goId: goId ?? this.goId,
            loId: loId ?? this.loId,
            workflowSource: workflowSource ?? this.workflowSource,
            functionType: functionType ?? this.functionType,
            agentType: agentType ?? this.agentType,
            executionRights: executionRights ?? this.executionRights,
        );

    factory ParsedLos.fromJson(Map<String, dynamic> json) => ParsedLos(
        name: json["name"],
        version: json["version"],
        status: json["status"],
        goId: json["go_id"],
        loId: json["lo_id"],
        workflowSource: json["workflow_source"],
        functionType: json["function_type"],
        agentType: json["agent_type"],
        executionRights: json["execution_rights"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "version": version,
        "status": status,
        "go_id": goId,
        "lo_id": loId,
        "workflow_source": workflowSource,
        "function_type": functionType,
        "agent_type": agentType,
        "execution_rights": executionRights,
    };
}

class ParsedGos {
    ParsedGos();

    ParsedGos copyWith(
    ) => 
        ParsedGos(
        );

    factory ParsedGos.fromJson(Map<String, dynamic> json) => ParsedGos(
    );

    Map<String, dynamic> toJson() => {
    };
}
