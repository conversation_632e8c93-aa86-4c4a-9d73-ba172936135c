// To parse this JSON data, do
//
//     final entityModelResponseModel = entityModelResponseModelFromJson(jsonString);

import 'dart:convert';

EntityModelResponseModel entityModelResponseModelFromJson(String str) => EntityModelResponseModel.fromJson(json.decode(str));

String entityModelResponseModelToJson(EntityModelResponseModel data) => json.encode(data.toJson());

class EntityModelResponseModel {
    bool? success;
    List<String>? messages;
    ParsedData? parsedData;
    dynamic validationErrors;
    Entities? parsedEntities;
    ParsedAttributes? parsedAttributes;
    ParsedAttributes? parsedRelationships;

    EntityModelResponseModel({
        this.success,
        this.messages,
        this.parsedData,
        this.validationErrors,
        this.parsedEntities,
        this.parsedAttributes,
        this.parsedRelationships,
    });

    EntityModelResponseModel copyWith({
        bool? success,
        List<String>? messages,
        ParsedData? parsedData,
        dynamic validationErrors,
        Entities? parsedEntities,
        ParsedAttributes? parsedAttributes,
        ParsedAttributes? parsedRelationships,
    }) => 
        EntityModelResponseModel(
            success: success ?? this.success,
            messages: messages ?? this.messages,
            parsedData: parsedData ?? this.parsedData,
            validationErrors: validationErrors ?? this.validationErrors,
            parsedEntities: parsedEntities ?? this.parsedEntities,
            parsedAttributes: parsedAttributes ?? this.parsedAttributes,
            parsedRelationships: parsedRelationships ?? this.parsedRelationships,
        );

    factory EntityModelResponseModel.fromJson(Map<String, dynamic> json) => EntityModelResponseModel(
        success: json["success"],
        messages: json["messages"] == null ? [] : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null ? null : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"],
        parsedEntities: json["parsed_entities"] == null ? null : Entities.fromJson(json["parsed_entities"]),
        parsedAttributes: json["parsed_attributes"] == null ? null : ParsedAttributes.fromJson(json["parsed_attributes"]),
        parsedRelationships: json["parsed_relationships"] == null ? null : ParsedAttributes.fromJson(json["parsed_relationships"]),
    );

    Map<String, dynamic> toJson() => {
        "success": success,
        "messages": messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors,
        "parsed_entities": parsedEntities?.toJson(),
        "parsed_attributes": parsedAttributes?.toJson(),
        "parsed_relationships": parsedRelationships?.toJson(),
    };
}

class ParsedAttributes {
    ParsedAttributes();

    ParsedAttributes copyWith(
    ) => 
        ParsedAttributes(
        );

    factory ParsedAttributes.fromJson(Map<String, dynamic> json) => ParsedAttributes(
    );

    Map<String, dynamic> toJson() => {
    };
}

class ParsedData {
    Entities? entities;

    ParsedData({
        this.entities,
    });

    ParsedData copyWith({
        Entities? entities,
    }) => 
        ParsedData(
            entities: entities ?? this.entities,
        );

    factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        entities: json["entities"] == null ? null : Entities.fromJson(json["entities"]),
    );

    Map<String, dynamic> toJson() => {
        "entities": entities?.toJson(),
    };
}

class Entities {
    Loan? loan;
    Customer? customer;
    Collateral? collateral;
    Payment? payment;

    Entities({
        this.loan,
        this.customer,
        this.collateral,
        this.payment,
    });

    Entities copyWith({
        Loan? loan,
        Customer? customer,
        Collateral? collateral,
        Payment? payment,
    }) => 
        Entities(
            loan: loan ?? this.loan,
            customer: customer ?? this.customer,
            collateral: collateral ?? this.collateral,
            payment: payment ?? this.payment,
        );

    factory Entities.fromJson(Map<String, dynamic> json) => Entities(
        loan: json["Loan"] == null ? null : Loan.fromJson(json["Loan"]),
        customer: json["Customer"] == null ? null : Customer.fromJson(json["Customer"]),
        collateral: json["Collateral"] == null ? null : Collateral.fromJson(json["Collateral"]),
        payment: json["Payment"] == null ? null : Payment.fromJson(json["Payment"]),
    );

    Map<String, dynamic> toJson() => {
        "Loan": loan?.toJson(),
        "Customer": customer?.toJson(),
        "Collateral": collateral?.toJson(),
        "Payment": payment?.toJson(),
    };
}

class Collateral {
    Name? name;
    Map<String, Attribute>? attributes;
    CollateralRelationships? relationships;
    ParsedAttributes? businessRules;
    ParsedAttributes? calculatedFields;
    CollateralValidations? validations;
    ParsedAttributes? constraints;
    Map<String, AttributeMetadatum>? attributeMetadata;
    CollateralRelationshipProperties? relationshipProperties;
    List<dynamic>? syntheticData;
    List<dynamic>? confidentialAttributes;
    List<dynamic>? internalAttributes;
    List<dynamic>? publicAttributes;
    ParsedAttributes? loadingStrategies;
    ParsedAttributes? archiveStrategy;
    ParsedAttributes? historyTracking;
    ParsedAttributes? workflow;
    ParsedAttributes? businessRulePlacement;
    ParsedAttributes? workflowPlacement;
    ParsedAttributes? entityPlacement;
    String? displayName;
    String? type;
    String? description;
    String? entityId;

    Collateral({
        this.name,
        this.attributes,
        this.relationships,
        this.businessRules,
        this.calculatedFields,
        this.validations,
        this.constraints,
        this.attributeMetadata,
        this.relationshipProperties,
        this.syntheticData,
        this.confidentialAttributes,
        this.internalAttributes,
        this.publicAttributes,
        this.loadingStrategies,
        this.archiveStrategy,
        this.historyTracking,
        this.workflow,
        this.businessRulePlacement,
        this.workflowPlacement,
        this.entityPlacement,
        this.displayName,
        this.type,
        this.description,
        this.entityId,
    });

    Collateral copyWith({
        Name? name,
        Map<String, Attribute>? attributes,
        CollateralRelationships? relationships,
        ParsedAttributes? businessRules,
        ParsedAttributes? calculatedFields,
        CollateralValidations? validations,
        ParsedAttributes? constraints,
        Map<String, AttributeMetadatum>? attributeMetadata,
        CollateralRelationshipProperties? relationshipProperties,
        List<dynamic>? syntheticData,
        List<dynamic>? confidentialAttributes,
        List<dynamic>? internalAttributes,
        List<dynamic>? publicAttributes,
        ParsedAttributes? loadingStrategies,
        ParsedAttributes? archiveStrategy,
        ParsedAttributes? historyTracking,
        ParsedAttributes? workflow,
        ParsedAttributes? businessRulePlacement,
        ParsedAttributes? workflowPlacement,
        ParsedAttributes? entityPlacement,
        String? displayName,
        String? type,
        String? description,
        String? entityId,
    }) => 
        Collateral(
            name: name ?? this.name,
            attributes: attributes ?? this.attributes,
            relationships: relationships ?? this.relationships,
            businessRules: businessRules ?? this.businessRules,
            calculatedFields: calculatedFields ?? this.calculatedFields,
            validations: validations ?? this.validations,
            constraints: constraints ?? this.constraints,
            attributeMetadata: attributeMetadata ?? this.attributeMetadata,
            relationshipProperties: relationshipProperties ?? this.relationshipProperties,
            syntheticData: syntheticData ?? this.syntheticData,
            confidentialAttributes: confidentialAttributes ?? this.confidentialAttributes,
            internalAttributes: internalAttributes ?? this.internalAttributes,
            publicAttributes: publicAttributes ?? this.publicAttributes,
            loadingStrategies: loadingStrategies ?? this.loadingStrategies,
            archiveStrategy: archiveStrategy ?? this.archiveStrategy,
            historyTracking: historyTracking ?? this.historyTracking,
            workflow: workflow ?? this.workflow,
            businessRulePlacement: businessRulePlacement ?? this.businessRulePlacement,
            workflowPlacement: workflowPlacement ?? this.workflowPlacement,
            entityPlacement: entityPlacement ?? this.entityPlacement,
            displayName: displayName ?? this.displayName,
            type: type ?? this.type,
            description: description ?? this.description,
            entityId: entityId ?? this.entityId,
        );

    factory Collateral.fromJson(Map<String, dynamic> json) => Collateral(
        name: nameValues.map[json["name"]]!,
        attributes: Map.from(json["attributes"]!).map((k, v) => MapEntry<String, Attribute>(k, Attribute.fromJson(v))),
        relationships: json["relationships"] == null ? null : CollateralRelationships.fromJson(json["relationships"]),
        businessRules: json["business_rules"] == null ? null : ParsedAttributes.fromJson(json["business_rules"]),
        calculatedFields: json["calculated_fields"] == null ? null : ParsedAttributes.fromJson(json["calculated_fields"]),
        validations: json["validations"] == null ? null : CollateralValidations.fromJson(json["validations"]),
        constraints: json["constraints"] == null ? null : ParsedAttributes.fromJson(json["constraints"]),
        attributeMetadata: Map.from(json["attribute_metadata"]!).map((k, v) => MapEntry<String, AttributeMetadatum>(k, AttributeMetadatum.fromJson(v))),
        relationshipProperties: json["relationship_properties"] == null ? null : CollateralRelationshipProperties.fromJson(json["relationship_properties"]),
        syntheticData: json["synthetic_data"] == null ? [] : List<dynamic>.from(json["synthetic_data"]!.map((x) => x)),
        confidentialAttributes: json["confidential_attributes"] == null ? [] : List<dynamic>.from(json["confidential_attributes"]!.map((x) => x)),
        internalAttributes: json["internal_attributes"] == null ? [] : List<dynamic>.from(json["internal_attributes"]!.map((x) => x)),
        publicAttributes: json["public_attributes"] == null ? [] : List<dynamic>.from(json["public_attributes"]!.map((x) => x)),
        loadingStrategies: json["loading_strategies"] == null ? null : ParsedAttributes.fromJson(json["loading_strategies"]),
        archiveStrategy: json["archive_strategy"] == null ? null : ParsedAttributes.fromJson(json["archive_strategy"]),
        historyTracking: json["history_tracking"] == null ? null : ParsedAttributes.fromJson(json["history_tracking"]),
        workflow: json["workflow"] == null ? null : ParsedAttributes.fromJson(json["workflow"]),
        businessRulePlacement: json["business_rule_placement"] == null ? null : ParsedAttributes.fromJson(json["business_rule_placement"]),
        workflowPlacement: json["workflow_placement"] == null ? null : ParsedAttributes.fromJson(json["workflow_placement"]),
        entityPlacement: json["entity_placement"] == null ? null : ParsedAttributes.fromJson(json["entity_placement"]),
        displayName: json["display_name"],
        type: json["type"],
        description: json["description"],
        entityId: json["entity_id"],
    );

    Map<String, dynamic> toJson() => {
        "name": nameValues.reverse[name],
        "attributes": Map.from(attributes!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationships": relationships?.toJson(),
        "business_rules": businessRules?.toJson(),
        "calculated_fields": calculatedFields?.toJson(),
        "validations": validations?.toJson(),
        "constraints": constraints?.toJson(),
        "attribute_metadata": Map.from(attributeMetadata!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationship_properties": relationshipProperties?.toJson(),
        "synthetic_data": syntheticData == null ? [] : List<dynamic>.from(syntheticData!.map((x) => x)),
        "confidential_attributes": confidentialAttributes == null ? [] : List<dynamic>.from(confidentialAttributes!.map((x) => x)),
        "internal_attributes": internalAttributes == null ? [] : List<dynamic>.from(internalAttributes!.map((x) => x)),
        "public_attributes": publicAttributes == null ? [] : List<dynamic>.from(publicAttributes!.map((x) => x)),
        "loading_strategies": loadingStrategies?.toJson(),
        "archive_strategy": archiveStrategy?.toJson(),
        "history_tracking": historyTracking?.toJson(),
        "workflow": workflow?.toJson(),
        "business_rule_placement": businessRulePlacement?.toJson(),
        "workflow_placement": workflowPlacement?.toJson(),
        "entity_placement": entityPlacement?.toJson(),
        "display_name": displayName,
        "type": type,
        "description": description,
        "entity_id": entityId,
    };
}

class AttributeMetadatum {
    KeyType? keyType;
    String? displayName;
    DataType? dataType;
    DataType? dataTypeFull;
    DataType? type;
    String? format;
    String? values;
    String? attributeMetadatumDefault;
    ValidationEnum? validation;
    String? errorMessage;
    String? description;

    AttributeMetadatum({
        this.keyType,
        this.displayName,
        this.dataType,
        this.dataTypeFull,
        this.type,
        this.format,
        this.values,
        this.attributeMetadatumDefault,
        this.validation,
        this.errorMessage,
        this.description,
    });

    AttributeMetadatum copyWith({
        KeyType? keyType,
        String? displayName,
        DataType? dataType,
        DataType? dataTypeFull,
        DataType? type,
        String? format,
        String? values,
        String? attributeMetadatumDefault,
        ValidationEnum? validation,
        String? errorMessage,
        String? description,
    }) => 
        AttributeMetadatum(
            keyType: keyType ?? this.keyType,
            displayName: displayName ?? this.displayName,
            dataType: dataType ?? this.dataType,
            dataTypeFull: dataTypeFull ?? this.dataTypeFull,
            type: type ?? this.type,
            format: format ?? this.format,
            values: values ?? this.values,
            attributeMetadatumDefault: attributeMetadatumDefault ?? this.attributeMetadatumDefault,
            validation: validation ?? this.validation,
            errorMessage: errorMessage ?? this.errorMessage,
            description: description ?? this.description,
        );

    factory AttributeMetadatum.fromJson(Map<String, dynamic> json) => AttributeMetadatum(
        keyType: keyTypeValues.map[json["key_type"]]!,
        displayName: json["display_name"],
        dataType: dataTypeValues.map[json["data_type"]]!,
        dataTypeFull: dataTypeValues.map[json["data_type_full"]]!,
        type: dataTypeValues.map[json["type"]]!,
        format: json["format"],
        values: json["values"],
        attributeMetadatumDefault: json["default"],
        validation: validationEnumValues.map[json["validation"]]!,
        errorMessage: json["error_message"],
        description: json["description"],
    );

    Map<String, dynamic> toJson() => {
        "key_type": keyTypeValues.reverse[keyType],
        "display_name": displayName,
        "data_type": dataTypeValues.reverse[dataType],
        "data_type_full": dataTypeValues.reverse[dataTypeFull],
        "type": dataTypeValues.reverse[type],
        "format": format,
        "values": values,
        "default": attributeMetadatumDefault,
        "validation": validationEnumValues.reverse[validation],
        "error_message": errorMessage,
        "description": description,
    };
}

enum DataType {
    BOOLEAN,
    DATA_TYPE_STRING,
    DATE,
    DECIMAL,
    ENUM,
    INTEGER,
    STRING
}

final dataTypeValues = EnumValues({
    "Boolean": DataType.BOOLEAN,
    "string": DataType.DATA_TYPE_STRING,
    "Date": DataType.DATE,
    "Decimal": DataType.DECIMAL,
    "Enum": DataType.ENUM,
    "Integer": DataType.INTEGER,
    "String": DataType.STRING
});

enum KeyType {
    FOREIGN,
    NON_UNIQUE,
    PRIMARY
}

final keyTypeValues = EnumValues({
    "Foreign": KeyType.FOREIGN,
    "Non-unique": KeyType.NON_UNIQUE,
    "Primary": KeyType.PRIMARY
});

enum ValidationEnum {
    AGE_CHECK,
    AUTO_INCREMENT,
    BOOLEAN_CHECK,
    CALCULATION,
    DATE_CHECK,
    FOREIGN_KEY_CHECK,
    FORMAT_CHECK,
    LENGTH_CHECK,
    LIST_CHECK,
    RANGE_CHECK
}

final validationEnumValues = EnumValues({
    "Age Check": ValidationEnum.AGE_CHECK,
    "Auto-increment": ValidationEnum.AUTO_INCREMENT,
    "Boolean Check": ValidationEnum.BOOLEAN_CHECK,
    "Calculation": ValidationEnum.CALCULATION,
    "Date Check": ValidationEnum.DATE_CHECK,
    "Foreign Key Check": ValidationEnum.FOREIGN_KEY_CHECK,
    "Format Check": ValidationEnum.FORMAT_CHECK,
    "Length Check": ValidationEnum.LENGTH_CHECK,
    "List Check": ValidationEnum.LIST_CHECK,
    "Range Check": ValidationEnum.RANGE_CHECK
});

class Attribute {
    String? name;
    bool? primaryKey;
    bool? foreignKey;
    bool? calculated;
    DataType? dataType;
    String? defaultValue;
    List<ValidationElement>? validations;
    List<String>? enumValues;

    Attribute({
        this.name,
        this.primaryKey,
        this.foreignKey,
        this.calculated,
        this.dataType,
        this.defaultValue,
        this.validations,
        this.enumValues,
    });

    Attribute copyWith({
        String? name,
        bool? primaryKey,
        bool? foreignKey,
        bool? calculated,
        DataType? dataType,
        String? defaultValue,
        List<ValidationElement>? validations,
        List<String>? enumValues,
    }) => 
        Attribute(
            name: name ?? this.name,
            primaryKey: primaryKey ?? this.primaryKey,
            foreignKey: foreignKey ?? this.foreignKey,
            calculated: calculated ?? this.calculated,
            dataType: dataType ?? this.dataType,
            defaultValue: defaultValue ?? this.defaultValue,
            validations: validations ?? this.validations,
            enumValues: enumValues ?? this.enumValues,
        );

    factory Attribute.fromJson(Map<String, dynamic> json) => Attribute(
        name: json["name"],
        primaryKey: json["primary_key"],
        foreignKey: json["foreign_key"],
        calculated: json["calculated"],
        dataType: dataTypeValues.map[json["data_type"]]!,
        defaultValue: json["default_value"],
        validations: json["validations"] == null ? [] : List<ValidationElement>.from(json["validations"]!.map((x) => ValidationElement.fromJson(x))),
        enumValues: json["enum_values"] == null ? [] : List<String>.from(json["enum_values"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "primary_key": primaryKey,
        "foreign_key": foreignKey,
        "calculated": calculated,
        "data_type": dataTypeValues.reverse[dataType],
        "default_value": defaultValue,
        "validations": validations == null ? [] : List<dynamic>.from(validations!.map((x) => x.toJson())),
        "enum_values": enumValues == null ? [] : List<dynamic>.from(enumValues!.map((x) => x)),
    };
}

class ValidationElement {
    String? constraint;
    Id? id;

    ValidationElement({
        this.constraint,
        this.id,
    });

    ValidationElement copyWith({
        String? constraint,
        Id? id,
    }) => 
        ValidationElement(
            constraint: constraint ?? this.constraint,
            id: id ?? this.id,
        );

    factory ValidationElement.fromJson(Map<String, dynamic> json) => ValidationElement(
        constraint: json["constraint"],
        id: idValues.map[json["id"]]!,
    );

    Map<String, dynamic> toJson() => {
        "constraint": constraint,
        "id": idValues.reverse[id],
    };
}

enum Id {
    VAL_1,
    VAL_2,
    VAL_3,
    VAL_4,
    VAL_5,
    VAL_6
}

final idValues = EnumValues({
    "val_1": Id.VAL_1,
    "val_2": Id.VAL_2,
    "val_3": Id.VAL_3,
    "val_4": Id.VAL_4,
    "val_5": Id.VAL_5,
    "val_6": Id.VAL_6
});

enum Name {
    COLLATERAL,
    CUSTOMER,
    LOAN,
    PAYMENT
}

final nameValues = EnumValues({
    "Collateral": Name.COLLATERAL,
    "Customer": Name.CUSTOMER,
    "Loan": Name.LOAN,
    "Payment": Name.PAYMENT
});

class CollateralRelationshipProperties {
    RelProps? relPropsCollateralCustomer;

    CollateralRelationshipProperties({
        this.relPropsCollateralCustomer,
    });

    CollateralRelationshipProperties copyWith({
        RelProps? relPropsCollateralCustomer,
    }) => 
        CollateralRelationshipProperties(
            relPropsCollateralCustomer: relPropsCollateralCustomer ?? this.relPropsCollateralCustomer,
        );

    factory CollateralRelationshipProperties.fromJson(Map<String, dynamic> json) => CollateralRelationshipProperties(
        relPropsCollateralCustomer: json["rel_props_Collateral_Customer"] == null ? null : RelProps.fromJson(json["rel_props_Collateral_Customer"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_props_Collateral_Customer": relPropsCollateralCustomer?.toJson(),
    };
}

class RelProps {
    Name? sourceEntity;
    Name? targetEntity;
    String? onDelete;
    String? onUpdate;
    String? foreignKeyType;

    RelProps({
        this.sourceEntity,
        this.targetEntity,
        this.onDelete,
        this.onUpdate,
        this.foreignKeyType,
    });

    RelProps copyWith({
        Name? sourceEntity,
        Name? targetEntity,
        String? onDelete,
        String? onUpdate,
        String? foreignKeyType,
    }) => 
        RelProps(
            sourceEntity: sourceEntity ?? this.sourceEntity,
            targetEntity: targetEntity ?? this.targetEntity,
            onDelete: onDelete ?? this.onDelete,
            onUpdate: onUpdate ?? this.onUpdate,
            foreignKeyType: foreignKeyType ?? this.foreignKeyType,
        );

    factory RelProps.fromJson(Map<String, dynamic> json) => RelProps(
        sourceEntity: nameValues.map[json["source_entity"]]!,
        targetEntity: nameValues.map[json["target_entity"]]!,
        onDelete: json["on_delete"],
        onUpdate: json["on_update"],
        foreignKeyType: json["foreign_key_type"],
    );

    Map<String, dynamic> toJson() => {
        "source_entity": nameValues.reverse[sourceEntity],
        "target_entity": nameValues.reverse[targetEntity],
        "on_delete": onDelete,
        "on_update": onUpdate,
        "foreign_key_type": foreignKeyType,
    };
}

class CollateralRelationships {
    Rel? rel1;
    Rel? rel2;

    CollateralRelationships({
        this.rel1,
        this.rel2,
    });

    CollateralRelationships copyWith({
        Rel? rel1,
        Rel? rel2,
    }) => 
        CollateralRelationships(
            rel1: rel1 ?? this.rel1,
            rel2: rel2 ?? this.rel2,
        );

    factory CollateralRelationships.fromJson(Map<String, dynamic> json) => CollateralRelationships(
        rel1: json["rel_1"] == null ? null : Rel.fromJson(json["rel_1"]),
        rel2: json["rel_2"] == null ? null : Rel.fromJson(json["rel_2"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_1": rel1?.toJson(),
        "rel_2": rel2?.toJson(),
    };
}

class Rel {
    Name? entity;
    Type? type;
    SourceAttributeEnum? sourceAttribute;
    SourceAttributeEnum? targetAttribute;
    Name? sourceEntity;
    Name? targetEntity;
    Cardinality? cardinality;
    String? joinCondition;
    CascadeOperations? cascadeOperations;

    Rel({
        this.entity,
        this.type,
        this.sourceAttribute,
        this.targetAttribute,
        this.sourceEntity,
        this.targetEntity,
        this.cardinality,
        this.joinCondition,
        this.cascadeOperations,
    });

    Rel copyWith({
        Name? entity,
        Type? type,
        SourceAttributeEnum? sourceAttribute,
        SourceAttributeEnum? targetAttribute,
        Name? sourceEntity,
        Name? targetEntity,
        Cardinality? cardinality,
        String? joinCondition,
        CascadeOperations? cascadeOperations,
    }) => 
        Rel(
            entity: entity ?? this.entity,
            type: type ?? this.type,
            sourceAttribute: sourceAttribute ?? this.sourceAttribute,
            targetAttribute: targetAttribute ?? this.targetAttribute,
            sourceEntity: sourceEntity ?? this.sourceEntity,
            targetEntity: targetEntity ?? this.targetEntity,
            cardinality: cardinality ?? this.cardinality,
            joinCondition: joinCondition ?? this.joinCondition,
            cascadeOperations: cascadeOperations ?? this.cascadeOperations,
        );

    factory Rel.fromJson(Map<String, dynamic> json) => Rel(
        entity: nameValues.map[json["entity"]]!,
        type: typeValues.map[json["type"]]!,
        sourceAttribute: sourceAttributeEnumValues.map[json["source_attribute"]]!,
        targetAttribute: sourceAttributeEnumValues.map[json["target_attribute"]]!,
        sourceEntity: nameValues.map[json["source_entity"]]!,
        targetEntity: nameValues.map[json["target_entity"]]!,
        cardinality: json["cardinality"] == null ? null : Cardinality.fromJson(json["cardinality"]),
        joinCondition: json["join_condition"],
        cascadeOperations: json["cascade_operations"] == null ? null : CascadeOperations.fromJson(json["cascade_operations"]),
    );

    Map<String, dynamic> toJson() => {
        "entity": nameValues.reverse[entity],
        "type": typeValues.reverse[type],
        "source_attribute": sourceAttributeEnumValues.reverse[sourceAttribute],
        "target_attribute": sourceAttributeEnumValues.reverse[targetAttribute],
        "source_entity": nameValues.reverse[sourceEntity],
        "target_entity": nameValues.reverse[targetEntity],
        "cardinality": cardinality?.toJson(),
        "join_condition": joinCondition,
        "cascade_operations": cascadeOperations?.toJson(),
    };
}

class Cardinality {
    String? source;
    String? target;

    Cardinality({
        this.source,
        this.target,
    });

    Cardinality copyWith({
        String? source,
        String? target,
    }) => 
        Cardinality(
            source: source ?? this.source,
            target: target ?? this.target,
        );

    factory Cardinality.fromJson(Map<String, dynamic> json) => Cardinality(
        source: json["source"],
        target: json["target"],
    );

    Map<String, dynamic> toJson() => {
        "source": source,
        "target": target,
    };
}

class CascadeOperations {
    Delete? delete;
    Update? update;

    CascadeOperations({
        this.delete,
        this.update,
    });

    CascadeOperations copyWith({
        Delete? delete,
        Update? update,
    }) => 
        CascadeOperations(
            delete: delete ?? this.delete,
            update: update ?? this.update,
        );

    factory CascadeOperations.fromJson(Map<String, dynamic> json) => CascadeOperations(
        delete: deleteValues.map[json["delete"]]!,
        update: updateValues.map[json["update"]]!,
    );

    Map<String, dynamic> toJson() => {
        "delete": deleteValues.reverse[delete],
        "update": updateValues.reverse[update],
    };
}

enum Delete {
    RESTRICT
}

final deleteValues = EnumValues({
    "restrict": Delete.RESTRICT
});

enum Update {
    CASCADE
}

final updateValues = EnumValues({
    "cascade": Update.CASCADE
});

enum SourceAttributeEnum {
    COLLATERAL_ID,
    CUSTOMER_ID,
    LOAN_ID
}

final sourceAttributeEnumValues = EnumValues({
    "collateralId": SourceAttributeEnum.COLLATERAL_ID,
    "customerId": SourceAttributeEnum.CUSTOMER_ID,
    "loanId": SourceAttributeEnum.LOAN_ID
});

enum Type {
    MANY_TO_ONE,
    ONE_TO_MANY
}

final typeValues = EnumValues({
    "many-to-one": Type.MANY_TO_ONE,
    "one-to-many": Type.ONE_TO_MANY
});

class CollateralValidations {
    Val1Class? val1;
    PurpleVal? val4;
    PurpleVal? val5;

    CollateralValidations({
        this.val1,
        this.val4,
        this.val5,
    });

    CollateralValidations copyWith({
        Val1Class? val1,
        PurpleVal? val4,
        PurpleVal? val5,
    }) => 
        CollateralValidations(
            val1: val1 ?? this.val1,
            val4: val4 ?? this.val4,
            val5: val5 ?? this.val5,
        );

    factory CollateralValidations.fromJson(Map<String, dynamic> json) => CollateralValidations(
        val1: json["val_1"] == null ? null : Val1Class.fromJson(json["val_1"]),
        val4: json["val_4"] == null ? null : PurpleVal.fromJson(json["val_4"]),
        val5: json["val_5"] == null ? null : PurpleVal.fromJson(json["val_5"]),
    );

    Map<String, dynamic> toJson() => {
        "val_1": val1?.toJson(),
        "val_4": val4?.toJson(),
        "val_5": val5?.toJson(),
    };
}

class Val1Class {
    String? attribute;
    String? constraintText;
    Name? entity;
    Val1ValidationType? validationType;
    ParsedAttributes? parameters;
    String? executableRule;

    Val1Class({
        this.attribute,
        this.constraintText,
        this.entity,
        this.validationType,
        this.parameters,
        this.executableRule,
    });

    Val1Class copyWith({
        String? attribute,
        String? constraintText,
        Name? entity,
        Val1ValidationType? validationType,
        ParsedAttributes? parameters,
        String? executableRule,
    }) => 
        Val1Class(
            attribute: attribute ?? this.attribute,
            constraintText: constraintText ?? this.constraintText,
            entity: entity ?? this.entity,
            validationType: validationType ?? this.validationType,
            parameters: parameters ?? this.parameters,
            executableRule: executableRule ?? this.executableRule,
        );

    factory Val1Class.fromJson(Map<String, dynamic> json) => Val1Class(
        attribute: json["attribute"],
        constraintText: json["constraint_text"],
        entity: nameValues.map[json["entity"]]!,
        validationType: val1ValidationTypeValues.map[json["validation_type"]]!,
        parameters: json["parameters"] == null ? null : ParsedAttributes.fromJson(json["parameters"]),
        executableRule: json["executable_rule"],
    );

    Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "constraint_text": constraintText,
        "entity": nameValues.reverse[entity],
        "validation_type": val1ValidationTypeValues.reverse[validationType],
        "parameters": parameters?.toJson(),
        "executable_rule": executableRule,
    };
}

enum Val1ValidationType {
    CUSTOM,
    ENUM,
    NOT_FUTURE,
    UNIQUE
}

final val1ValidationTypeValues = EnumValues({
    "custom": Val1ValidationType.CUSTOM,
    "enum": Val1ValidationType.ENUM,
    "not_future": Val1ValidationType.NOT_FUTURE,
    "unique": Val1ValidationType.UNIQUE
});

class PurpleVal {
    String? attribute;
    ConstraintText? constraintText;
    Name? entity;
    PurpleValidationType? validationType;
    Val5Parameters? parameters;
    String? executableRule;

    PurpleVal({
        this.attribute,
        this.constraintText,
        this.entity,
        this.validationType,
        this.parameters,
        this.executableRule,
    });

    PurpleVal copyWith({
        String? attribute,
        ConstraintText? constraintText,
        Name? entity,
        PurpleValidationType? validationType,
        Val5Parameters? parameters,
        String? executableRule,
    }) => 
        PurpleVal(
            attribute: attribute ?? this.attribute,
            constraintText: constraintText ?? this.constraintText,
            entity: entity ?? this.entity,
            validationType: validationType ?? this.validationType,
            parameters: parameters ?? this.parameters,
            executableRule: executableRule ?? this.executableRule,
        );

    factory PurpleVal.fromJson(Map<String, dynamic> json) => PurpleVal(
        attribute: json["attribute"],
        constraintText: constraintTextValues.map[json["constraint_text"]]!,
        entity: nameValues.map[json["entity"]]!,
        validationType: purpleValidationTypeValues.map[json["validation_type"]]!,
        parameters: json["parameters"] == null ? null : Val5Parameters.fromJson(json["parameters"]),
        executableRule: json["executable_rule"],
    );

    Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "constraint_text": constraintTextValues.reverse[constraintText],
        "entity": nameValues.reverse[entity],
        "validation_type": purpleValidationTypeValues.reverse[validationType],
        "parameters": parameters?.toJson(),
        "executable_rule": executableRule,
    };
}

enum ConstraintText {
    BE_GREATER_THAN_0
}

final constraintTextValues = EnumValues({
    "be greater than 0": ConstraintText.BE_GREATER_THAN_0
});

class Val5Parameters {
    String? value;

    Val5Parameters({
        this.value,
    });

    Val5Parameters copyWith({
        String? value,
    }) => 
        Val5Parameters(
            value: value ?? this.value,
        );

    factory Val5Parameters.fromJson(Map<String, dynamic> json) => Val5Parameters(
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "value": value,
    };
}

enum PurpleValidationType {
    GREATER_THAN
}

final purpleValidationTypeValues = EnumValues({
    "greater_than": PurpleValidationType.GREATER_THAN
});

class Customer {
    Name? name;
    Map<String, Attribute>? attributes;
    CollateralRelationships? relationships;
    ParsedAttributes? businessRules;
    ParsedAttributes? calculatedFields;
    CustomerValidations? validations;
    ParsedAttributes? constraints;
    Map<String, AttributeMetadatum>? attributeMetadata;
    CustomerRelationshipProperties? relationshipProperties;
    List<dynamic>? syntheticData;
    List<dynamic>? confidentialAttributes;
    List<dynamic>? internalAttributes;
    List<dynamic>? publicAttributes;
    ParsedAttributes? loadingStrategies;
    ParsedAttributes? archiveStrategy;
    ParsedAttributes? historyTracking;
    ParsedAttributes? workflow;
    ParsedAttributes? businessRulePlacement;
    ParsedAttributes? workflowPlacement;
    ParsedAttributes? entityPlacement;
    Name? displayName;
    String? type;
    String? description;
    String? entityId;

    Customer({
        this.name,
        this.attributes,
        this.relationships,
        this.businessRules,
        this.calculatedFields,
        this.validations,
        this.constraints,
        this.attributeMetadata,
        this.relationshipProperties,
        this.syntheticData,
        this.confidentialAttributes,
        this.internalAttributes,
        this.publicAttributes,
        this.loadingStrategies,
        this.archiveStrategy,
        this.historyTracking,
        this.workflow,
        this.businessRulePlacement,
        this.workflowPlacement,
        this.entityPlacement,
        this.displayName,
        this.type,
        this.description,
        this.entityId,
    });

    Customer copyWith({
        Name? name,
        Map<String, Attribute>? attributes,
        CollateralRelationships? relationships,
        ParsedAttributes? businessRules,
        ParsedAttributes? calculatedFields,
        CustomerValidations? validations,
        ParsedAttributes? constraints,
        Map<String, AttributeMetadatum>? attributeMetadata,
        CustomerRelationshipProperties? relationshipProperties,
        List<dynamic>? syntheticData,
        List<dynamic>? confidentialAttributes,
        List<dynamic>? internalAttributes,
        List<dynamic>? publicAttributes,
        ParsedAttributes? loadingStrategies,
        ParsedAttributes? archiveStrategy,
        ParsedAttributes? historyTracking,
        ParsedAttributes? workflow,
        ParsedAttributes? businessRulePlacement,
        ParsedAttributes? workflowPlacement,
        ParsedAttributes? entityPlacement,
        Name? displayName,
        String? type,
        String? description,
        String? entityId,
    }) => 
        Customer(
            name: name ?? this.name,
            attributes: attributes ?? this.attributes,
            relationships: relationships ?? this.relationships,
            businessRules: businessRules ?? this.businessRules,
            calculatedFields: calculatedFields ?? this.calculatedFields,
            validations: validations ?? this.validations,
            constraints: constraints ?? this.constraints,
            attributeMetadata: attributeMetadata ?? this.attributeMetadata,
            relationshipProperties: relationshipProperties ?? this.relationshipProperties,
            syntheticData: syntheticData ?? this.syntheticData,
            confidentialAttributes: confidentialAttributes ?? this.confidentialAttributes,
            internalAttributes: internalAttributes ?? this.internalAttributes,
            publicAttributes: publicAttributes ?? this.publicAttributes,
            loadingStrategies: loadingStrategies ?? this.loadingStrategies,
            archiveStrategy: archiveStrategy ?? this.archiveStrategy,
            historyTracking: historyTracking ?? this.historyTracking,
            workflow: workflow ?? this.workflow,
            businessRulePlacement: businessRulePlacement ?? this.businessRulePlacement,
            workflowPlacement: workflowPlacement ?? this.workflowPlacement,
            entityPlacement: entityPlacement ?? this.entityPlacement,
            displayName: displayName ?? this.displayName,
            type: type ?? this.type,
            description: description ?? this.description,
            entityId: entityId ?? this.entityId,
        );

    factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        name: nameValues.map[json["name"]]!,
        attributes: Map.from(json["attributes"]!).map((k, v) => MapEntry<String, Attribute>(k, Attribute.fromJson(v))),
        relationships: json["relationships"] == null ? null : CollateralRelationships.fromJson(json["relationships"]),
        businessRules: json["business_rules"] == null ? null : ParsedAttributes.fromJson(json["business_rules"]),
        calculatedFields: json["calculated_fields"] == null ? null : ParsedAttributes.fromJson(json["calculated_fields"]),
        validations: json["validations"] == null ? null : CustomerValidations.fromJson(json["validations"]),
        constraints: json["constraints"] == null ? null : ParsedAttributes.fromJson(json["constraints"]),
        attributeMetadata: Map.from(json["attribute_metadata"]!).map((k, v) => MapEntry<String, AttributeMetadatum>(k, AttributeMetadatum.fromJson(v))),
        relationshipProperties: json["relationship_properties"] == null ? null : CustomerRelationshipProperties.fromJson(json["relationship_properties"]),
        syntheticData: json["synthetic_data"] == null ? [] : List<dynamic>.from(json["synthetic_data"]!.map((x) => x)),
        confidentialAttributes: json["confidential_attributes"] == null ? [] : List<dynamic>.from(json["confidential_attributes"]!.map((x) => x)),
        internalAttributes: json["internal_attributes"] == null ? [] : List<dynamic>.from(json["internal_attributes"]!.map((x) => x)),
        publicAttributes: json["public_attributes"] == null ? [] : List<dynamic>.from(json["public_attributes"]!.map((x) => x)),
        loadingStrategies: json["loading_strategies"] == null ? null : ParsedAttributes.fromJson(json["loading_strategies"]),
        archiveStrategy: json["archive_strategy"] == null ? null : ParsedAttributes.fromJson(json["archive_strategy"]),
        historyTracking: json["history_tracking"] == null ? null : ParsedAttributes.fromJson(json["history_tracking"]),
        workflow: json["workflow"] == null ? null : ParsedAttributes.fromJson(json["workflow"]),
        businessRulePlacement: json["business_rule_placement"] == null ? null : ParsedAttributes.fromJson(json["business_rule_placement"]),
        workflowPlacement: json["workflow_placement"] == null ? null : ParsedAttributes.fromJson(json["workflow_placement"]),
        entityPlacement: json["entity_placement"] == null ? null : ParsedAttributes.fromJson(json["entity_placement"]),
        displayName: nameValues.map[json["display_name"]]!,
        type: json["type"],
        description: json["description"],
        entityId: json["entity_id"],
    );

    Map<String, dynamic> toJson() => {
        "name": nameValues.reverse[name],
        "attributes": Map.from(attributes!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationships": relationships?.toJson(),
        "business_rules": businessRules?.toJson(),
        "calculated_fields": calculatedFields?.toJson(),
        "validations": validations?.toJson(),
        "constraints": constraints?.toJson(),
        "attribute_metadata": Map.from(attributeMetadata!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationship_properties": relationshipProperties?.toJson(),
        "synthetic_data": syntheticData == null ? [] : List<dynamic>.from(syntheticData!.map((x) => x)),
        "confidential_attributes": confidentialAttributes == null ? [] : List<dynamic>.from(confidentialAttributes!.map((x) => x)),
        "internal_attributes": internalAttributes == null ? [] : List<dynamic>.from(internalAttributes!.map((x) => x)),
        "public_attributes": publicAttributes == null ? [] : List<dynamic>.from(publicAttributes!.map((x) => x)),
        "loading_strategies": loadingStrategies?.toJson(),
        "archive_strategy": archiveStrategy?.toJson(),
        "history_tracking": historyTracking?.toJson(),
        "workflow": workflow?.toJson(),
        "business_rule_placement": businessRulePlacement?.toJson(),
        "workflow_placement": workflowPlacement?.toJson(),
        "entity_placement": entityPlacement?.toJson(),
        "display_name": nameValues.reverse[displayName],
        "type": type,
        "description": description,
        "entity_id": entityId,
    };
}

class CustomerRelationshipProperties {
    RelProps? relPropsCustomerLoan;

    CustomerRelationshipProperties({
        this.relPropsCustomerLoan,
    });

    CustomerRelationshipProperties copyWith({
        RelProps? relPropsCustomerLoan,
    }) => 
        CustomerRelationshipProperties(
            relPropsCustomerLoan: relPropsCustomerLoan ?? this.relPropsCustomerLoan,
        );

    factory CustomerRelationshipProperties.fromJson(Map<String, dynamic> json) => CustomerRelationshipProperties(
        relPropsCustomerLoan: json["rel_props_Customer_Loan"] == null ? null : RelProps.fromJson(json["rel_props_Customer_Loan"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_props_Customer_Loan": relPropsCustomerLoan?.toJson(),
    };
}

class CustomerValidations {
    Val1Class? val1;
    Val1Class? val2;
    Val1Class? val3;
    Val4? val4;
    PurpleVal? val5;

    CustomerValidations({
        this.val1,
        this.val2,
        this.val3,
        this.val4,
        this.val5,
    });

    CustomerValidations copyWith({
        Val1Class? val1,
        Val1Class? val2,
        Val1Class? val3,
        Val4? val4,
        PurpleVal? val5,
    }) => 
        CustomerValidations(
            val1: val1 ?? this.val1,
            val2: val2 ?? this.val2,
            val3: val3 ?? this.val3,
            val4: val4 ?? this.val4,
            val5: val5 ?? this.val5,
        );

    factory CustomerValidations.fromJson(Map<String, dynamic> json) => CustomerValidations(
        val1: json["val_1"] == null ? null : Val1Class.fromJson(json["val_1"]),
        val2: json["val_2"] == null ? null : Val1Class.fromJson(json["val_2"]),
        val3: json["val_3"] == null ? null : Val1Class.fromJson(json["val_3"]),
        val4: json["val_4"] == null ? null : Val4.fromJson(json["val_4"]),
        val5: json["val_5"] == null ? null : PurpleVal.fromJson(json["val_5"]),
    );

    Map<String, dynamic> toJson() => {
        "val_1": val1?.toJson(),
        "val_2": val2?.toJson(),
        "val_3": val3?.toJson(),
        "val_4": val4?.toJson(),
        "val_5": val5?.toJson(),
    };
}

class Val4 {
    String? attribute;
    String? constraintText;
    Name? entity;
    String? validationType;
    PurpleParameters? parameters;
    String? executableRule;

    Val4({
        this.attribute,
        this.constraintText,
        this.entity,
        this.validationType,
        this.parameters,
        this.executableRule,
    });

    Val4 copyWith({
        String? attribute,
        String? constraintText,
        Name? entity,
        String? validationType,
        PurpleParameters? parameters,
        String? executableRule,
    }) => 
        Val4(
            attribute: attribute ?? this.attribute,
            constraintText: constraintText ?? this.constraintText,
            entity: entity ?? this.entity,
            validationType: validationType ?? this.validationType,
            parameters: parameters ?? this.parameters,
            executableRule: executableRule ?? this.executableRule,
        );

    factory Val4.fromJson(Map<String, dynamic> json) => Val4(
        attribute: json["attribute"],
        constraintText: json["constraint_text"],
        entity: nameValues.map[json["entity"]]!,
        validationType: json["validation_type"],
        parameters: json["parameters"] == null ? null : PurpleParameters.fromJson(json["parameters"]),
        executableRule: json["executable_rule"],
    );

    Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "constraint_text": constraintText,
        "entity": nameValues.reverse[entity],
        "validation_type": validationType,
        "parameters": parameters?.toJson(),
        "executable_rule": executableRule,
    };
}

class PurpleParameters {
    String? minValue;
    String? maxValue;

    PurpleParameters({
        this.minValue,
        this.maxValue,
    });

    PurpleParameters copyWith({
        String? minValue,
        String? maxValue,
    }) => 
        PurpleParameters(
            minValue: minValue ?? this.minValue,
            maxValue: maxValue ?? this.maxValue,
        );

    factory PurpleParameters.fromJson(Map<String, dynamic> json) => PurpleParameters(
        minValue: json["min_value"],
        maxValue: json["max_value"],
    );

    Map<String, dynamic> toJson() => {
        "min_value": minValue,
        "max_value": maxValue,
    };
}

class Loan {
    Name? name;
    Map<String, Attribute>? attributes;
    CollateralRelationships? relationships;
    ParsedAttributes? businessRules;
    ParsedAttributes? calculatedFields;
    LoanValidations? validations;
    ParsedAttributes? constraints;
    Map<String, AttributeMetadatum>? attributeMetadata;
    LoanRelationshipProperties? relationshipProperties;
    List<dynamic>? syntheticData;
    List<dynamic>? confidentialAttributes;
    List<dynamic>? internalAttributes;
    List<dynamic>? publicAttributes;
    ParsedAttributes? loadingStrategies;
    ParsedAttributes? archiveStrategy;
    ParsedAttributes? historyTracking;
    ParsedAttributes? workflow;
    ParsedAttributes? businessRulePlacement;
    ParsedAttributes? workflowPlacement;
    ParsedAttributes? entityPlacement;
    String? displayName;
    String? type;
    String? description;
    String? entityId;

    Loan({
        this.name,
        this.attributes,
        this.relationships,
        this.businessRules,
        this.calculatedFields,
        this.validations,
        this.constraints,
        this.attributeMetadata,
        this.relationshipProperties,
        this.syntheticData,
        this.confidentialAttributes,
        this.internalAttributes,
        this.publicAttributes,
        this.loadingStrategies,
        this.archiveStrategy,
        this.historyTracking,
        this.workflow,
        this.businessRulePlacement,
        this.workflowPlacement,
        this.entityPlacement,
        this.displayName,
        this.type,
        this.description,
        this.entityId,
    });

    Loan copyWith({
        Name? name,
        Map<String, Attribute>? attributes,
        CollateralRelationships? relationships,
        ParsedAttributes? businessRules,
        ParsedAttributes? calculatedFields,
        LoanValidations? validations,
        ParsedAttributes? constraints,
        Map<String, AttributeMetadatum>? attributeMetadata,
        LoanRelationshipProperties? relationshipProperties,
        List<dynamic>? syntheticData,
        List<dynamic>? confidentialAttributes,
        List<dynamic>? internalAttributes,
        List<dynamic>? publicAttributes,
        ParsedAttributes? loadingStrategies,
        ParsedAttributes? archiveStrategy,
        ParsedAttributes? historyTracking,
        ParsedAttributes? workflow,
        ParsedAttributes? businessRulePlacement,
        ParsedAttributes? workflowPlacement,
        ParsedAttributes? entityPlacement,
        String? displayName,
        String? type,
        String? description,
        String? entityId,
    }) => 
        Loan(
            name: name ?? this.name,
            attributes: attributes ?? this.attributes,
            relationships: relationships ?? this.relationships,
            businessRules: businessRules ?? this.businessRules,
            calculatedFields: calculatedFields ?? this.calculatedFields,
            validations: validations ?? this.validations,
            constraints: constraints ?? this.constraints,
            attributeMetadata: attributeMetadata ?? this.attributeMetadata,
            relationshipProperties: relationshipProperties ?? this.relationshipProperties,
            syntheticData: syntheticData ?? this.syntheticData,
            confidentialAttributes: confidentialAttributes ?? this.confidentialAttributes,
            internalAttributes: internalAttributes ?? this.internalAttributes,
            publicAttributes: publicAttributes ?? this.publicAttributes,
            loadingStrategies: loadingStrategies ?? this.loadingStrategies,
            archiveStrategy: archiveStrategy ?? this.archiveStrategy,
            historyTracking: historyTracking ?? this.historyTracking,
            workflow: workflow ?? this.workflow,
            businessRulePlacement: businessRulePlacement ?? this.businessRulePlacement,
            workflowPlacement: workflowPlacement ?? this.workflowPlacement,
            entityPlacement: entityPlacement ?? this.entityPlacement,
            displayName: displayName ?? this.displayName,
            type: type ?? this.type,
            description: description ?? this.description,
            entityId: entityId ?? this.entityId,
        );

    factory Loan.fromJson(Map<String, dynamic> json) => Loan(
        name: nameValues.map[json["name"]]!,
        attributes: Map.from(json["attributes"]!).map((k, v) => MapEntry<String, Attribute>(k, Attribute.fromJson(v))),
        relationships: json["relationships"] == null ? null : CollateralRelationships.fromJson(json["relationships"]),
        businessRules: json["business_rules"] == null ? null : ParsedAttributes.fromJson(json["business_rules"]),
        calculatedFields: json["calculated_fields"] == null ? null : ParsedAttributes.fromJson(json["calculated_fields"]),
        validations: json["validations"] == null ? null : LoanValidations.fromJson(json["validations"]),
        constraints: json["constraints"] == null ? null : ParsedAttributes.fromJson(json["constraints"]),
        attributeMetadata: Map.from(json["attribute_metadata"]!).map((k, v) => MapEntry<String, AttributeMetadatum>(k, AttributeMetadatum.fromJson(v))),
        relationshipProperties: json["relationship_properties"] == null ? null : LoanRelationshipProperties.fromJson(json["relationship_properties"]),
        syntheticData: json["synthetic_data"] == null ? [] : List<dynamic>.from(json["synthetic_data"]!.map((x) => x)),
        confidentialAttributes: json["confidential_attributes"] == null ? [] : List<dynamic>.from(json["confidential_attributes"]!.map((x) => x)),
        internalAttributes: json["internal_attributes"] == null ? [] : List<dynamic>.from(json["internal_attributes"]!.map((x) => x)),
        publicAttributes: json["public_attributes"] == null ? [] : List<dynamic>.from(json["public_attributes"]!.map((x) => x)),
        loadingStrategies: json["loading_strategies"] == null ? null : ParsedAttributes.fromJson(json["loading_strategies"]),
        archiveStrategy: json["archive_strategy"] == null ? null : ParsedAttributes.fromJson(json["archive_strategy"]),
        historyTracking: json["history_tracking"] == null ? null : ParsedAttributes.fromJson(json["history_tracking"]),
        workflow: json["workflow"] == null ? null : ParsedAttributes.fromJson(json["workflow"]),
        businessRulePlacement: json["business_rule_placement"] == null ? null : ParsedAttributes.fromJson(json["business_rule_placement"]),
        workflowPlacement: json["workflow_placement"] == null ? null : ParsedAttributes.fromJson(json["workflow_placement"]),
        entityPlacement: json["entity_placement"] == null ? null : ParsedAttributes.fromJson(json["entity_placement"]),
        displayName: json["display_name"],
        type: json["type"],
        description: json["description"],
        entityId: json["entity_id"],
    );

    Map<String, dynamic> toJson() => {
        "name": nameValues.reverse[name],
        "attributes": Map.from(attributes!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationships": relationships?.toJson(),
        "business_rules": businessRules?.toJson(),
        "calculated_fields": calculatedFields?.toJson(),
        "validations": validations?.toJson(),
        "constraints": constraints?.toJson(),
        "attribute_metadata": Map.from(attributeMetadata!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationship_properties": relationshipProperties?.toJson(),
        "synthetic_data": syntheticData == null ? [] : List<dynamic>.from(syntheticData!.map((x) => x)),
        "confidential_attributes": confidentialAttributes == null ? [] : List<dynamic>.from(confidentialAttributes!.map((x) => x)),
        "internal_attributes": internalAttributes == null ? [] : List<dynamic>.from(internalAttributes!.map((x) => x)),
        "public_attributes": publicAttributes == null ? [] : List<dynamic>.from(publicAttributes!.map((x) => x)),
        "loading_strategies": loadingStrategies?.toJson(),
        "archive_strategy": archiveStrategy?.toJson(),
        "history_tracking": historyTracking?.toJson(),
        "workflow": workflow?.toJson(),
        "business_rule_placement": businessRulePlacement?.toJson(),
        "workflow_placement": workflowPlacement?.toJson(),
        "entity_placement": entityPlacement?.toJson(),
        "display_name": displayName,
        "type": type,
        "description": description,
        "entity_id": entityId,
    };
}

class LoanRelationshipProperties {
    RelProps? relPropsLoanCustomer;

    LoanRelationshipProperties({
        this.relPropsLoanCustomer,
    });

    LoanRelationshipProperties copyWith({
        RelProps? relPropsLoanCustomer,
    }) => 
        LoanRelationshipProperties(
            relPropsLoanCustomer: relPropsLoanCustomer ?? this.relPropsLoanCustomer,
        );

    factory LoanRelationshipProperties.fromJson(Map<String, dynamic> json) => LoanRelationshipProperties(
        relPropsLoanCustomer: json["rel_props_Loan_Customer"] == null ? null : RelProps.fromJson(json["rel_props_Loan_Customer"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_props_Loan_Customer": relPropsLoanCustomer?.toJson(),
    };
}

class LoanValidations {
    Val1Class? val1;
    PurpleVal? val2;
    FluffyVal? val3;
    FluffyVal? val4;
    Val1Class? val5;
    Val6? val6;

    LoanValidations({
        this.val1,
        this.val2,
        this.val3,
        this.val4,
        this.val5,
        this.val6,
    });

    LoanValidations copyWith({
        Val1Class? val1,
        PurpleVal? val2,
        FluffyVal? val3,
        FluffyVal? val4,
        Val1Class? val5,
        Val6? val6,
    }) => 
        LoanValidations(
            val1: val1 ?? this.val1,
            val2: val2 ?? this.val2,
            val3: val3 ?? this.val3,
            val4: val4 ?? this.val4,
            val5: val5 ?? this.val5,
            val6: val6 ?? this.val6,
        );

    factory LoanValidations.fromJson(Map<String, dynamic> json) => LoanValidations(
        val1: json["val_1"] == null ? null : Val1Class.fromJson(json["val_1"]),
        val2: json["val_2"] == null ? null : PurpleVal.fromJson(json["val_2"]),
        val3: json["val_3"] == null ? null : FluffyVal.fromJson(json["val_3"]),
        val4: json["val_4"] == null ? null : FluffyVal.fromJson(json["val_4"]),
        val5: json["val_5"] == null ? null : Val1Class.fromJson(json["val_5"]),
        val6: json["val_6"] == null ? null : Val6.fromJson(json["val_6"]),
    );

    Map<String, dynamic> toJson() => {
        "val_1": val1?.toJson(),
        "val_2": val2?.toJson(),
        "val_3": val3?.toJson(),
        "val_4": val4?.toJson(),
        "val_5": val5?.toJson(),
        "val_6": val6?.toJson(),
    };
}

class FluffyVal {
    String? attribute;
    String? constraintText;
    Name? entity;
    String? validationType;
    FluffyParameters? parameters;
    String? executableRule;

    FluffyVal({
        this.attribute,
        this.constraintText,
        this.entity,
        this.validationType,
        this.parameters,
        this.executableRule,
    });

    FluffyVal copyWith({
        String? attribute,
        String? constraintText,
        Name? entity,
        String? validationType,
        FluffyParameters? parameters,
        String? executableRule,
    }) => 
        FluffyVal(
            attribute: attribute ?? this.attribute,
            constraintText: constraintText ?? this.constraintText,
            entity: entity ?? this.entity,
            validationType: validationType ?? this.validationType,
            parameters: parameters ?? this.parameters,
            executableRule: executableRule ?? this.executableRule,
        );

    factory FluffyVal.fromJson(Map<String, dynamic> json) => FluffyVal(
        attribute: json["attribute"],
        constraintText: json["constraint_text"],
        entity: nameValues.map[json["entity"]]!,
        validationType: json["validation_type"],
        parameters: json["parameters"] == null ? null : FluffyParameters.fromJson(json["parameters"]),
        executableRule: json["executable_rule"],
    );

    Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "constraint_text": constraintText,
        "entity": nameValues.reverse[entity],
        "validation_type": validationType,
        "parameters": parameters?.toJson(),
        "executable_rule": executableRule,
    };
}

class FluffyParameters {
    String? minValue;

    FluffyParameters({
        this.minValue,
    });

    FluffyParameters copyWith({
        String? minValue,
    }) => 
        FluffyParameters(
            minValue: minValue ?? this.minValue,
        );

    factory FluffyParameters.fromJson(Map<String, dynamic> json) => FluffyParameters(
        minValue: json["min_value"],
    );

    Map<String, dynamic> toJson() => {
        "min_value": minValue,
    };
}

class Val6 {
    String? attribute;
    String? constraintText;
    Name? entity;
    String? validationType;
    Val6Parameters? parameters;
    String? executableRule;

    Val6({
        this.attribute,
        this.constraintText,
        this.entity,
        this.validationType,
        this.parameters,
        this.executableRule,
    });

    Val6 copyWith({
        String? attribute,
        String? constraintText,
        Name? entity,
        String? validationType,
        Val6Parameters? parameters,
        String? executableRule,
    }) => 
        Val6(
            attribute: attribute ?? this.attribute,
            constraintText: constraintText ?? this.constraintText,
            entity: entity ?? this.entity,
            validationType: validationType ?? this.validationType,
            parameters: parameters ?? this.parameters,
            executableRule: executableRule ?? this.executableRule,
        );

    factory Val6.fromJson(Map<String, dynamic> json) => Val6(
        attribute: json["attribute"],
        constraintText: json["constraint_text"],
        entity: nameValues.map[json["entity"]]!,
        validationType: json["validation_type"],
        parameters: json["parameters"] == null ? null : Val6Parameters.fromJson(json["parameters"]),
        executableRule: json["executable_rule"],
    );

    Map<String, dynamic> toJson() => {
        "attribute": attribute,
        "constraint_text": constraintText,
        "entity": nameValues.reverse[entity],
        "validation_type": validationType,
        "parameters": parameters?.toJson(),
        "executable_rule": executableRule,
    };
}

class Val6Parameters {
    String? referenceAttribute;

    Val6Parameters({
        this.referenceAttribute,
    });

    Val6Parameters copyWith({
        String? referenceAttribute,
    }) => 
        Val6Parameters(
            referenceAttribute: referenceAttribute ?? this.referenceAttribute,
        );

    factory Val6Parameters.fromJson(Map<String, dynamic> json) => Val6Parameters(
        referenceAttribute: json["reference_attribute"],
    );

    Map<String, dynamic> toJson() => {
        "reference_attribute": referenceAttribute,
    };
}

class Payment {
    Name? name;
    Map<String, Attribute>? attributes;
    PaymentRelationships? relationships;
    ParsedAttributes? businessRules;
    ParsedAttributes? calculatedFields;
    PaymentValidations? validations;
    ParsedAttributes? constraints;
    Map<String, AttributeMetadatum>? attributeMetadata;
    PaymentRelationshipProperties? relationshipProperties;
    List<dynamic>? syntheticData;
    List<dynamic>? confidentialAttributes;
    List<dynamic>? internalAttributes;
    List<dynamic>? publicAttributes;
    ParsedAttributes? loadingStrategies;
    ParsedAttributes? archiveStrategy;
    ParsedAttributes? historyTracking;
    ParsedAttributes? workflow;
    ParsedAttributes? businessRulePlacement;
    ParsedAttributes? workflowPlacement;
    ParsedAttributes? entityPlacement;
    String? displayName;
    String? type;
    String? description;
    String? entityId;

    Payment({
        this.name,
        this.attributes,
        this.relationships,
        this.businessRules,
        this.calculatedFields,
        this.validations,
        this.constraints,
        this.attributeMetadata,
        this.relationshipProperties,
        this.syntheticData,
        this.confidentialAttributes,
        this.internalAttributes,
        this.publicAttributes,
        this.loadingStrategies,
        this.archiveStrategy,
        this.historyTracking,
        this.workflow,
        this.businessRulePlacement,
        this.workflowPlacement,
        this.entityPlacement,
        this.displayName,
        this.type,
        this.description,
        this.entityId,
    });

    Payment copyWith({
        Name? name,
        Map<String, Attribute>? attributes,
        PaymentRelationships? relationships,
        ParsedAttributes? businessRules,
        ParsedAttributes? calculatedFields,
        PaymentValidations? validations,
        ParsedAttributes? constraints,
        Map<String, AttributeMetadatum>? attributeMetadata,
        PaymentRelationshipProperties? relationshipProperties,
        List<dynamic>? syntheticData,
        List<dynamic>? confidentialAttributes,
        List<dynamic>? internalAttributes,
        List<dynamic>? publicAttributes,
        ParsedAttributes? loadingStrategies,
        ParsedAttributes? archiveStrategy,
        ParsedAttributes? historyTracking,
        ParsedAttributes? workflow,
        ParsedAttributes? businessRulePlacement,
        ParsedAttributes? workflowPlacement,
        ParsedAttributes? entityPlacement,
        String? displayName,
        String? type,
        String? description,
        String? entityId,
    }) => 
        Payment(
            name: name ?? this.name,
            attributes: attributes ?? this.attributes,
            relationships: relationships ?? this.relationships,
            businessRules: businessRules ?? this.businessRules,
            calculatedFields: calculatedFields ?? this.calculatedFields,
            validations: validations ?? this.validations,
            constraints: constraints ?? this.constraints,
            attributeMetadata: attributeMetadata ?? this.attributeMetadata,
            relationshipProperties: relationshipProperties ?? this.relationshipProperties,
            syntheticData: syntheticData ?? this.syntheticData,
            confidentialAttributes: confidentialAttributes ?? this.confidentialAttributes,
            internalAttributes: internalAttributes ?? this.internalAttributes,
            publicAttributes: publicAttributes ?? this.publicAttributes,
            loadingStrategies: loadingStrategies ?? this.loadingStrategies,
            archiveStrategy: archiveStrategy ?? this.archiveStrategy,
            historyTracking: historyTracking ?? this.historyTracking,
            workflow: workflow ?? this.workflow,
            businessRulePlacement: businessRulePlacement ?? this.businessRulePlacement,
            workflowPlacement: workflowPlacement ?? this.workflowPlacement,
            entityPlacement: entityPlacement ?? this.entityPlacement,
            displayName: displayName ?? this.displayName,
            type: type ?? this.type,
            description: description ?? this.description,
            entityId: entityId ?? this.entityId,
        );

    factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        name: nameValues.map[json["name"]]!,
        attributes: Map.from(json["attributes"]!).map((k, v) => MapEntry<String, Attribute>(k, Attribute.fromJson(v))),
        relationships: json["relationships"] == null ? null : PaymentRelationships.fromJson(json["relationships"]),
        businessRules: json["business_rules"] == null ? null : ParsedAttributes.fromJson(json["business_rules"]),
        calculatedFields: json["calculated_fields"] == null ? null : ParsedAttributes.fromJson(json["calculated_fields"]),
        validations: json["validations"] == null ? null : PaymentValidations.fromJson(json["validations"]),
        constraints: json["constraints"] == null ? null : ParsedAttributes.fromJson(json["constraints"]),
        attributeMetadata: Map.from(json["attribute_metadata"]!).map((k, v) => MapEntry<String, AttributeMetadatum>(k, AttributeMetadatum.fromJson(v))),
        relationshipProperties: json["relationship_properties"] == null ? null : PaymentRelationshipProperties.fromJson(json["relationship_properties"]),
        syntheticData: json["synthetic_data"] == null ? [] : List<dynamic>.from(json["synthetic_data"]!.map((x) => x)),
        confidentialAttributes: json["confidential_attributes"] == null ? [] : List<dynamic>.from(json["confidential_attributes"]!.map((x) => x)),
        internalAttributes: json["internal_attributes"] == null ? [] : List<dynamic>.from(json["internal_attributes"]!.map((x) => x)),
        publicAttributes: json["public_attributes"] == null ? [] : List<dynamic>.from(json["public_attributes"]!.map((x) => x)),
        loadingStrategies: json["loading_strategies"] == null ? null : ParsedAttributes.fromJson(json["loading_strategies"]),
        archiveStrategy: json["archive_strategy"] == null ? null : ParsedAttributes.fromJson(json["archive_strategy"]),
        historyTracking: json["history_tracking"] == null ? null : ParsedAttributes.fromJson(json["history_tracking"]),
        workflow: json["workflow"] == null ? null : ParsedAttributes.fromJson(json["workflow"]),
        businessRulePlacement: json["business_rule_placement"] == null ? null : ParsedAttributes.fromJson(json["business_rule_placement"]),
        workflowPlacement: json["workflow_placement"] == null ? null : ParsedAttributes.fromJson(json["workflow_placement"]),
        entityPlacement: json["entity_placement"] == null ? null : ParsedAttributes.fromJson(json["entity_placement"]),
        displayName: json["display_name"],
        type: json["type"],
        description: json["description"],
        entityId: json["entity_id"],
    );

    Map<String, dynamic> toJson() => {
        "name": nameValues.reverse[name],
        "attributes": Map.from(attributes!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationships": relationships?.toJson(),
        "business_rules": businessRules?.toJson(),
        "calculated_fields": calculatedFields?.toJson(),
        "validations": validations?.toJson(),
        "constraints": constraints?.toJson(),
        "attribute_metadata": Map.from(attributeMetadata!).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "relationship_properties": relationshipProperties?.toJson(),
        "synthetic_data": syntheticData == null ? [] : List<dynamic>.from(syntheticData!.map((x) => x)),
        "confidential_attributes": confidentialAttributes == null ? [] : List<dynamic>.from(confidentialAttributes!.map((x) => x)),
        "internal_attributes": internalAttributes == null ? [] : List<dynamic>.from(internalAttributes!.map((x) => x)),
        "public_attributes": publicAttributes == null ? [] : List<dynamic>.from(publicAttributes!.map((x) => x)),
        "loading_strategies": loadingStrategies?.toJson(),
        "archive_strategy": archiveStrategy?.toJson(),
        "history_tracking": historyTracking?.toJson(),
        "workflow": workflow?.toJson(),
        "business_rule_placement": businessRulePlacement?.toJson(),
        "workflow_placement": workflowPlacement?.toJson(),
        "entity_placement": entityPlacement?.toJson(),
        "display_name": displayName,
        "type": type,
        "description": description,
        "entity_id": entityId,
    };
}

class PaymentRelationshipProperties {
    RelProps? relPropsPaymentLoan;

    PaymentRelationshipProperties({
        this.relPropsPaymentLoan,
    });

    PaymentRelationshipProperties copyWith({
        RelProps? relPropsPaymentLoan,
    }) => 
        PaymentRelationshipProperties(
            relPropsPaymentLoan: relPropsPaymentLoan ?? this.relPropsPaymentLoan,
        );

    factory PaymentRelationshipProperties.fromJson(Map<String, dynamic> json) => PaymentRelationshipProperties(
        relPropsPaymentLoan: json["rel_props_Payment_Loan"] == null ? null : RelProps.fromJson(json["rel_props_Payment_Loan"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_props_Payment_Loan": relPropsPaymentLoan?.toJson(),
    };
}

class PaymentRelationships {
    Rel? rel1;

    PaymentRelationships({
        this.rel1,
    });

    PaymentRelationships copyWith({
        Rel? rel1,
    }) => 
        PaymentRelationships(
            rel1: rel1 ?? this.rel1,
        );

    factory PaymentRelationships.fromJson(Map<String, dynamic> json) => PaymentRelationships(
        rel1: json["rel_1"] == null ? null : Rel.fromJson(json["rel_1"]),
    );

    Map<String, dynamic> toJson() => {
        "rel_1": rel1?.toJson(),
    };
}

class PaymentValidations {
    Val1Class? val1;
    PurpleVal? val3;
    Val1Class? val4;
    Val1Class? val5;
    Val1Class? val6;

    PaymentValidations({
        this.val1,
        this.val3,
        this.val4,
        this.val5,
        this.val6,
    });

    PaymentValidations copyWith({
        Val1Class? val1,
        PurpleVal? val3,
        Val1Class? val4,
        Val1Class? val5,
        Val1Class? val6,
    }) => 
        PaymentValidations(
            val1: val1 ?? this.val1,
            val3: val3 ?? this.val3,
            val4: val4 ?? this.val4,
            val5: val5 ?? this.val5,
            val6: val6 ?? this.val6,
        );

    factory PaymentValidations.fromJson(Map<String, dynamic> json) => PaymentValidations(
        val1: json["val_1"] == null ? null : Val1Class.fromJson(json["val_1"]),
        val3: json["val_3"] == null ? null : PurpleVal.fromJson(json["val_3"]),
        val4: json["val_4"] == null ? null : Val1Class.fromJson(json["val_4"]),
        val5: json["val_5"] == null ? null : Val1Class.fromJson(json["val_5"]),
        val6: json["val_6"] == null ? null : Val1Class.fromJson(json["val_6"]),
    );

    Map<String, dynamic> toJson() => {
        "val_1": val1?.toJson(),
        "val_3": val3?.toJson(),
        "val_4": val4?.toJson(),
        "val_5": val5?.toJson(),
        "val_6": val6?.toJson(),
    };
}

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
            reverseMap = map.map((k, v) => MapEntry(v, k));
            return reverseMap;
    }
}
