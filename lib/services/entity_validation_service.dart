import 'package:dio/dio.dart';
import '../models/workflow/entity_manual_response_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling entity validation API operations
class EntityValidationService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8100';
  static const String _validateEndpoint = '/api/entities/validate';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  /// Validate text using the entities validation API
  Future<EntityModelResponseModel> validateText(String text) async {
    try {
      Logger.info('Validating entity text: ${text.substring(0, text.length > 50 ? 50 : text.length)}...');

      // Prepare form data
      final formData = FormData.fromMap({
        'text': text,
      });

      // Get a valid token (optional for this endpoint, but good practice)
      final token = await _authService.getValidToken();
      
      final options = Options(
        headers: {
          'Content-Type': 'multipart/form-data',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      // Make the API call
      final fullUrl = '$_baseUrl$_validateEndpoint';
      
      final response = await dio.post(
        fullUrl,
        data: formData,
        options: options,
      );

      Logger.info('Entity validation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Entity validation successful');
        return EntityModelResponseModel.fromJson(response.data);
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to validate entity text';
        Logger.error('Failed to validate entity text: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      Logger.error('Exception during entity validation: $e');
      if (e is DioException) {
        if (e.response != null) {
          final errorMessage = e.response?.data['message'] ?? 'Entity validation failed';
          throw Exception(errorMessage);
        } else {
          throw Exception('Network error during entity validation');
        }
      }
      throw Exception('Failed to validate entity text: $e');
    }
  }

  @override
  Future<String?> getValidToken() async {
    return await _authService.getValidToken();
  }

  @override
  Future<String?> getUserId() async {
    return await _authService.getUserId();
  }
}
