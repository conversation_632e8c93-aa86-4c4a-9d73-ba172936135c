import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/add_modules_mobileview.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';

import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 101.0,
    this.imageHeight = 156.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 101.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 156.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({
    super.key,
    this.showNavigationBar = true,
  });

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile>
    with TickerProviderStateMixin {
  late List<BookMobile> books;
  bool isLoading = true;
  int selectedTabIndex = 0; // 0: Books, 1: Solutions, 2: Objects
  late List<AnimationController> _animationControllers;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;
  
  // Swiper related variables
  late PageController _pageController;
  int _currentPage = 0;
  List<List<BookMobile>> _bookPages = [];
  static const int _itemsPerPage = 4; // 2 rows × 2 cards = 4 items per page

  // JSON string containing book data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Couri & Logistics Courier & Logistics ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fit & Wellness ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadBooks();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to BookMobile objects
      final List<BookMobile> loadedBooks = (data['books'] as List)
          .map((bookJson) => BookMobile.fromJson(bookJson))
          .toList();

      // Create pages with 4 items each
      _bookPages = [];
      for (int i = 0; i < loadedBooks.length; i += _itemsPerPage) {
        int end = (i + _itemsPerPage < loadedBooks.length) 
            ? i + _itemsPerPage 
            : loadedBooks.length;
        _bookPages.add(loadedBooks.sublist(i, end));
      }

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });

      // Initialize animations after loading books
      _initializeAnimations();
    } catch (e) {
      setState(() {
        books = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      books.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(-1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutCubic,
      ));
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ));
    }).toList();

    // Start animations with staggered delay
    _startStaggeredAnimations();
  }

  void _startStaggeredAnimations() {
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Always return the books library view directly since navigation is handled by MobileNavigationWrapper
    return _buildBooksLibraryView();
  }

  // Extract the original build content into a separate method
  Widget _buildBooksLibraryView() {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildBooksGrid(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {
              // Already on books screen, no navigation needed
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }

  Widget _buildBooksGrid() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: books.isEmpty
            ? const Center(child: Text('No books found'))
            : Column(
                children: [
                  // Swiper with PageView
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                      child: _bookPages.isEmpty
                          ? const Center(child: Text('No books found'))
                          : PageView.builder(
                              controller: _pageController,
                              onPageChanged: (index) {
                                setState(() {
                                  _currentPage = index;
                                });
                              },
                              itemCount: _bookPages.length,
                              itemBuilder: (context, pageIndex) {
                                return _buildBookPage(_bookPages[pageIndex], pageIndex);
                              },
                            ),
                    ),
                  ),
                  // Dot pagination
                  if (_bookPages.length > 1)
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          _bookPages.length,
                          (index) => GestureDetector(
                            onTap: () {
                              _pageController.animateToPage(
                                index,
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            child: Container(
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _currentPage == index
                                    ? const Color(0xff0058FF)
                                    : Colors.grey.shade300,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
      ),
    );
  }

  Widget _buildBookPage(List<BookMobile> pageBooks, int pageIndex) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the width for 3 full cards + 1 partially visible card
        double availableWidth = constraints.maxWidth;
        double spacing = 16.0; // Increased spacing to prevent overlap
        
        // Calculate width for each card accounting for proper spacing
        // 2 full cards + 2 spacing gaps + partial 3rd card (10% visible)
        double cardWidth = (availableWidth - (spacing * 2)) / 2.1;

        // Group books into rows of 3 (2 full + 1 partial)
        List<Widget> rows = [];
        
        for (int i = 0; i < pageBooks.length; i += 3) {
          List<BookMobile> rowBooks = [];
          for (int j = 0; j < 3 && (i + j) < pageBooks.length; j++) {
            rowBooks.add(pageBooks[i + j]);
          }
          
          // Get the 3rd book for partial visibility if available
          BookMobile? partialBook;
          if ((i + 2) < pageBooks.length) {
            partialBook = pageBooks[i + 2];
          } else {
            // Get from next page if available
            int globalIndex = (pageIndex * _itemsPerPage) + i + 2;
            if (globalIndex < books.length) {
              partialBook = books[globalIndex];
            }
          }
          
          rows.add(_buildBookRow(rowBooks, partialBook, cardWidth, spacing, availableWidth));
        }

        return SingleChildScrollView(
          child: Column(
            children: rows,
          ),
        );
      },
    );
  }

  Widget _buildBookRow(List<BookMobile> rowBooks, BookMobile? partialBook, double cardWidth, double spacing, double availableWidth) {
    List<Widget> rowWidgets = [];
    
    // Add all cards in the row
    for (int i = 0; i < rowBooks.length; i++) {
      if (i > 0) {
        rowWidgets.add(SizedBox(width: spacing));
      }
      
      // For the 3rd card (index 2), show it as partial (10% visible)
      if (i == 2) {
        rowWidgets.add(
          ClipRect(
            child: SizedBox(
              width: cardWidth * 0.1, // Show only 10% of the 3rd card
              child: _buildBookCard(rowBooks[i], cardWidth, true),
            ),
          ),
        );
      } else {
        // Show first 2 cards as full cards
        rowWidgets.add(
          SizedBox(
            width: cardWidth,
            child: _buildBookCard(rowBooks[i], cardWidth, false),
          ),
        );
      }
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: rowWidgets,
      ),
    );
  }

  Widget _buildBookCard(BookMobile book, double cardWidth, bool isHalfCard) {
    int globalIndex = books.indexOf(book);
    double imageHeight = (cardWidth * 156) / 101; // Maintain aspect ratio
    
    Widget bookCard = _MobileBookCard(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AddModulesMobileView(),
          ),
        );
      },
      child: _buildMobileBookCard(
        title: book.title,
        subtitle: book.subtitle,
        imageUrl: book.imageUrl,
        isDraft: book.isDraft,
        imageWidth: cardWidth,
        imageHeight: imageHeight,
        index: globalIndex,
        isHalfCard: isHalfCard,
      ),
    );

    // Apply animations if available
    if (globalIndex < _animationControllers.length) {
      return SlideTransition(
        position: _slideAnimations[globalIndex],
        child: FadeTransition(
          opacity: _fadeAnimations[globalIndex],
          child: bookCard,
        ),
      );
    }
    
    return bookCard;
  }

  Widget _buildMobileBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 101.0,
    double imageHeight = 156.0,
    int index = 0,
    bool isHalfCard = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Book cover
        Stack(
          clipBehavior: Clip.none,
          children: [
            // Background SVG shape
            if (!isHalfCard)
              Positioned(
                right: -7,
                bottom: 0,
                child: SvgPicture.asset(
                  'assets/images/home-lib-shape.svg',
                  width: imageWidth * 0.925,
                  height: imageHeight * 0.95,
                  fit: BoxFit.contain,
                ),
              ),
            // Main book image
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(24),
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: imageHeight * 0.08,
                right: imageWidth * 0.14,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: isHalfCard ? 10 : 12,
              height: 1.334,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: isHalfCard ? 1 : 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: imageWidth,
          child: Text(
            subtitle,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: isHalfCard ? 9 : 11,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Book Card Widget
class _MobileBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileBookCard> createState() => _MobileBookCardState();
}

class _MobileBookCardState extends State<_MobileBookCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
