import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/theme/spacing.dart';

class CustomAlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback onClose;

  final String? primaryButtonText;
  final VoidCallback? onPrimaryPressed;
  final Color? primaryButtonColor; // <-- New

  final String? secondaryButtonText;
  final VoidCallback? onSecondaryPressed;
  final Color? secondaryButtonColor; // <-- New

  const CustomAlertDialog({
    super.key,
    required this.title,
    required this.content,
    required this.onClose,
    this.primaryButtonText,
    this.onPrimaryPressed,
    this.primaryButtonColor, // <-- New
    this.secondaryButtonText,
    this.onSecondaryPressed,
    this.secondaryButtonColor, // <-- New
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppSpacing.sm)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 40),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 360),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title & Close
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context).translate(title),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'TestTiemposText',
                      ),
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: onClose,
                      child: const Icon(Icons.close, size: 20),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Content
              Text(
                AppLocalizations.of(context).translate(content),
                style: const TextStyle(fontSize: 14, color: Colors.black),
              ),
              const SizedBox(height: 24),

              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (secondaryButtonText != null)
                    OutlinedButton(
                      onPressed: onSecondaryPressed,
                      style: OutlinedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        side: BorderSide(color: secondaryButtonColor ?? Colors.black),
                      ),
                      child: Text(
                        AppLocalizations.of(context).translate(secondaryButtonText!),
                        style: TextStyle(color: secondaryButtonColor ?? Colors.black),
                      ),
                    ),
                  if (primaryButtonText != null) ...[
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: onPrimaryPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryButtonColor ?? Colors.blue, // default blue
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context).translate(primaryButtonText!),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
