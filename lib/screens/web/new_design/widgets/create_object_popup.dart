import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CreateObjectPopup extends StatelessWidget {
  final VoidCallback onAIGeneratedTap;
  final VoidCallback onManualCreationTap;

  const CreateObjectPopup({
    super.key,
    required this.onAIGeneratedTap,
    required this.onManualCreationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: 200,
        //height:95,
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey.shade500, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
        //  mainAxisSize: MainAxisSize.min,
          children: [
            // AI Generated option
            _PopupOption(
           image:   SvgPicture.asset(
                'assets/images/sparkle-stars-ai.svg',
              ),
              text: 'AI Generated',
              onTap: onAIGeneratedTap,
            ),
            

            // // Divider
            // Container(
            //   height: 1,
            //   color: Colors.grey.shade200,
            //   margin: const EdgeInsets.symmetric(horizontal: 12),
            // ),

            // Manual Creation option
            _PopupOption(
            image:   SvgPicture.asset(
                'assets/images/file-text.svg',
              ),
              text: 'Manual Creation',
              onTap: onManualCreationTap,
            ),
          ],
        ),
      ),
    );
  }
}

class _PopupOption extends StatefulWidget {
  final Widget image;
  final String text;
  final VoidCallback onTap;

  const _PopupOption({
    required this.image,
    required this.text,
    required this.onTap,
  });

  @override
  State<_PopupOption> createState() => _PopupOptionState();
}

class _PopupOptionState extends State<_PopupOption> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color:  Colors.white,
            // border: Border(
            //   bottom: BorderSide(
            //     color: Colors.grey.shade500,
            //     width: 1,
            //   ),
           // ),
            //isHovered ? const Color(0xFFFFC1CB) : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              widget.image,
              const SizedBox(width: 8),
              Text(
                widget.text,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
