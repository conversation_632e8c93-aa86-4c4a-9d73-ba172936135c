import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_book_solution_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebBookSolutionPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  State<WebBookSolutionPage> createState() => _WebBookSolutionPageState();
}

class _WebBookSolutionPageState extends State<WebBookSolutionPage> {
  bool _showRightPanel = false;
  bool _isExpanded = false;
  bool _showUploadModal = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          body: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: SizedBox()),
              Expanded(
                flex: 9,
                child: Row(
                  children: [
                    // Main content
                    Expanded(
                      child: Column(
                        children: [
                          _buildHeader(context, provider),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: AppSpacing.lg),
                              child: provider.showAddModules
                                  ? _buildAddModulesContent(context, provider)
                                  : _buildSolutionsList(provider),
                            ),
                          ),
                          ChatField(
                            isGeneralLoading: false,
                            isFileLoading: false,
                            isSpeechLoading: false,
                            onSendMessage: () {},
                            onFileSelected: (fileName, filePath) {},
                            onToggleRecording: () {},
                            controller: provider.chatController,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                flex: _isExpanded ? 4 : 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _showRightPanel = !_showRightPanel;
                            _isExpanded = !_isExpanded;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: 12), // adjust value as needed
                          child: AnimatedRotation(
                            turns: _isExpanded
                                ? 0.5
                                : 0.0, // 0.5 turns = 180 degrees
                            duration: Duration(milliseconds: 300),
                            child: SvgPicture.asset(
                              'assets/images/expand-arrow-left-new.svg',
                              width: AppSpacing.md,
                              height: AppSpacing.md,
                              colorFilter: ColorFilter.mode(
                                Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Right panel
                    if (_showRightPanel)
                      Container(
                        width: 352,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            left: BorderSide(
                                color: Colors.grey.shade200, width: 1),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.03),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Header
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 10),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(
                                  bottom: BorderSide(
                                      color: Colors.grey.shade200, width: 1),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'All Documents/Artefacts',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 13,
                                      color: Colors.black,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: _showUploadModal
                                          ? Color(0xFFccdeff)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                          color: _showUploadModal
                                              ? Color(0xFF0058FF)
                                              : Colors.grey.shade200,
                                          width: 1),
                                    ),
                                    child: IconButton(
                                      icon: Icon(
                                          _showUploadModal
                                              ? Icons.close
                                              : Icons.add,
                                          color: _showUploadModal
                                              ? Colors.black
                                              : Colors.black,
                                          size: 16),
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        setState(() {
                                          _showUploadModal = !_showUploadModal;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Scrollable content area
                            Expanded(
                              child: Container(
                                color: Colors.white,
                                child: SingleChildScrollView(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 8),
                                    child: _showUploadModal
                                        ? _buildUploadModal()
                                        : Container(), // Show upload modal when active
                                  ),
                                ),
                              ),
                            ),
                            // Bottom input
                            Container(
                              height: 112,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(
                                  color: Colors.grey.shade200,
                                  width: 1,
                                ),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                  bottomLeft: Radius.circular(0),
                                  bottomRight: Radius.circular(0),
                                ),
                              ),
                              child: TextField(
                                maxLines: 6, // or null for infinite lines
                                minLines: 4,
                                keyboardType: TextInputType.multiline,
                                decoration: InputDecoration(
                                  hintText: 'Add Instructions',
                                  hintStyle: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey.shade500),
                                  filled: true,
                                  fillColor: Color(0xFFFFFFFF),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                    borderSide: BorderSide(
                                        color: Colors.grey.shade200, width: 1),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                    borderSide: BorderSide(
                                        color: Colors.grey.shade200, width: 1),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                    borderSide: BorderSide(
                                        color: Colors.blue, width: 1),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(left: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section
          Row(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(4),
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyLibrary;
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Icon(
                          Icons.arrow_back,
                          color: Colors.grey,
                          size: AppSpacing.md,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Book Name',
                    style: TextStyle(
                      fontSize: AppSpacing.size14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                ],
              ),
              const SizedBox(width: AppSpacing.xs), // Add Modules section
              MouseRegion(
                cursor: SystemMouseCursors.click,
                onEnter: (_) {
                  provider.isAddModulesHeaderButtonHovering = true;
                },
                onExit: (_) {
                  provider.isAddModulesHeaderButtonHovering = false;
                },
                child: GestureDetector(
                  onTap: provider.toggleAddModules,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.transparent,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          'assets/images/add-module.svg',
                          width: AppSpacing.size14,
                          height: AppSpacing.size14,
                          colorFilter: ColorFilter.mode(
                            Colors.grey.shade600,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Add Modules',
                          style: TextStyle(
                            fontSize: AppSpacing.size14,
                            color: Colors.black,
                            fontFamily: 'TiemposText',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: AppSpacing.xxs),
                        Icon(
                          Icons.arrow_drop_down,
                          size: AppSpacing.lg,
                          color: provider.isAddModulesHeaderButtonHovering ||
                                  provider.showAddModules
                              ? const Color(0xff0058FF)
                              : Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Center section with count items grouped together
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCountItem('assets/images/agent.svg', '3 Agent (V001)'),
              const SizedBox(width: AppSpacing.lg),
              _buildCountItem(
                  'assets/images/cube-box.svg', '12 Objects (V001)'),
              const SizedBox(width: AppSpacing.lg),
              _buildCountItem(
                  'assets/images/square-box-uncheck.svg', '15 Solutions'),
            ],
          ),

          Row(
            children: [SizedBox(width: 80)],
          ) // Right section - expand button against the right edge
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              iconPath,
              width: AppSpacing.size14,
              height: AppSpacing.size14,
              colorFilter: ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              text,
              style: const TextStyle(
                fontSize: AppSpacing.size14,
                fontFamily: 'TiemposText',
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionsList(WebBookSolutionProvider provider) {
    return ListView.builder(
      itemCount: provider.solutionItems.length,
      itemBuilder: (context, index) {
        final item = provider.solutionItems[index];
        return _buildSolutionItem(item);
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 16,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Color(0xffD0D0D0)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: const TextStyle(
                        fontSize: AppSpacing.size14,
                        color: Color(0xff000000),
                        fontWeight: FontWeight.w600,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xxs),
                    Text(
                      'Last Message: ${item.lastMessageTime}',
                      style: TextStyle(
                        fontSize: AppSpacing.sm,
                        fontFamily: 'TiemposText',
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

              // Right content
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/folder.svg',
                        width: AppSpacing.md,
                        height: AppSpacing.md,
                        colorFilter: ColorFilter.mode(
                          Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xxs),
                      Text(
                        item.versionId,
                        style: TextStyle(
                          fontSize: AppSpacing.sm,
                          fontFamily: 'TiemposText',
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.xxs),
                  Text(
                    item.date,
                    style: TextStyle(
                      fontSize: AppSpacing.sm,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddModulesContent(
      BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header row
              Container(
                height: AppSpacing.xxl,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Text(
                          'Drag Your Solutions',
                          style: TextStyle(
                            fontSize: AppSpacing.size14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(left: 0),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Modules',
                              style: TextStyle(
                                fontSize: AppSpacing.sm,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                            CompositedTransformTarget(
                              link: provider.modulesButtonLink,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: Container(
                                  key: provider.modulesButtonKey,
                                  margin: EdgeInsets.symmetric(horizontal: 0),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey.shade900),
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.white,
                                  ),
                                  child: GestureDetector(
                                    onTap: () {
                                      if (provider.showModulesPopup) {
                                        provider.hideModulesPopupMenu();
                                      } else {
                                        provider.showModulesPopupMenu(context);
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Icon(Icons.add,
                                            size: AppSpacing.md,
                                            color: Colors.grey.shade700),
                                        SizedBox(width: AppSpacing.xxs),
                                        Text('Modules',
                                            style: TextStyle(
                                                fontSize: AppSpacing.sm)),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Expanded(child: Container()),
                  ],
                ),
              ),
              // Content row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left panel - Solutions list
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 40),
                              child: ListView.builder(
                                itemCount: provider.solutionOptions.length,
                                itemBuilder: (context, index) {
                                  return MouseRegion(
                                    cursor: SystemMouseCursors.click,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 6, horizontal: 10),
                                      child: Text(
                                        'Solutions-${(index + 1).toString().padLeft(2, '0')}',
                                        style: TextStyle(
                                            fontSize: AppSpacing.size14,
                                            color: Colors.black),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                height: AppSpacing.size40,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border(
                                    top:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.end, // Move to right
                                  children: [
                                    // Left chevron (no hover effect)
                                    _PaginationChevronButton(isLeft: true),
                                    SizedBox(width: AppSpacing.xs),
                                    // Right chevron with hover effect
                                    _PaginationChevronButton(isLeft: false),
                                    SizedBox(width: AppSpacing.xl),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Middle panel - Modules
                    Flexible(
                      flex: 1,
                      child: Container(
                        key: provider.modulesColumnKey,
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildModulesSection(provider),
                      ),
                    ),
                    // Right panel - empty
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    // Empty panel on the right
                    Expanded(
                      child: Container(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Cross button in top right
          Positioned(
            top: 8,
            right: 8,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: provider.toggleAddModules,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade200,
                  ),
                  padding: EdgeInsets.all(4),
                  child:
                      Icon(Icons.close, size: 20, color: Colors.grey.shade700),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModulesSection(WebBookSolutionProvider provider) {
    if (provider.modules.isEmpty) {
      return Container(); // Return empty container when no modules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < provider.modules.length; i++)
          _buildModuleItem(provider.modules[i], i, provider),
      ],
    );
  }

  Widget _buildModuleItem(
      ModuleItem module, int index, WebBookSolutionProvider provider) {
    return Container(
      color: Color(0xFFF8F9FA),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Static dropdown arrow for visual consistency
          Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              module.name,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppSpacing.md,
              ),
            ),
          ),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              key: provider.submoduleButtonKeys[index],
              onTap: () {
                provider.setActiveModulePopupIndex(index);
                provider.showSubmodulePopup(index);
              },
              child: Icon(Icons.add, size: AppSpacing.size18),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadModal() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 1),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: IntrinsicWidth(
            // Makes width fit the content
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Upload a file option
                Container(
                  padding: EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                    ),
                  ),
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        // Handle file upload
                      },
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/images/attached-2.svg',
                            width: 14,
                            height: 14,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'Upload a File',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                              fontFamily: 'Inter',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Add from Google Drive option

                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      // Handle Google Drive integration
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/G-Drive.svg',
                          width: 14,
                          height: 14,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Add From Google Drive',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            fontFamily: 'Inter',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _PaginationChevronButton extends StatelessWidget {
  final bool isLeft;
  const _PaginationChevronButton({this.isLeft = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        final isHovering = isLeft
            ? provider.isPaginationLeftChevronHovering
            : provider.isPaginationRightChevronHovering;

        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) {
            if (isLeft) {
              provider.isPaginationLeftChevronHovering = true;
            } else {
              provider.isPaginationRightChevronHovering = true;
            }
          },
          onExit: (_) {
            if (isLeft) {
              provider.isPaginationLeftChevronHovering = false;
            } else {
              provider.isPaginationRightChevronHovering = false;
            }
          },
          child: GestureDetector(
            child: AnimatedContainer(
              duration: Duration(milliseconds: 120),
              decoration: BoxDecoration(
                color: isHovering ? Color(0xFFF5F7FA) : Colors.transparent,
                border: isHovering
                    ? Border.all(color: Color(0xFFBDBDBD))
                    : Border.all(color: Colors.transparent),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                isLeft ? Icons.chevron_left : Icons.chevron_right,
                size: 22,
                color: Colors.grey.shade600,
              ),
            ),
          ),
        );
      },
    );
  }
}
