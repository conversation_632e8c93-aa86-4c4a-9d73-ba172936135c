import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../../providers/manual_creation_provider.dart';
import '../../../models/role_info.dart';
import '../../../widgets/resizable_panel.dart';
import 'widgets/chat_widgets/build_role_card.dart';
import 'widgets/role_details_panel.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _ManualCreationScreenContent();
  }
}

class _ManualCreationScreenContent extends StatefulWidget {
  @override
  _ManualCreationScreenContentState createState() =>
      _ManualCreationScreenContentState();
}

class _ManualCreationScreenContentState
    extends State<_ManualCreationScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;
  String? _activeIconView = "Agents"; // Track which icon view is active

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(
      BuildContext context, ManualCreationProvider provider) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                provider.showSidePanel
                    ? SizedBox(
                        width: AppSpacing.spaceBetweenMainContentAndSidePanel,
                        child: rightSideTooltips(provider),
                      )
                    : Expanded(
                        child: rightSideTooltips(provider),
                      ),

                // Main content area
                Expanded(
                  flex: provider.showSidePanel ? 3 : 7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: AppSpacing.sm),
                      // Header with back button
                      GestureDetector(
                        onTap: () {
                          Provider.of<WebHomeProvider>(context, listen: false)
                              .currentScreenIndex = ScreenConstants.home;
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/arrow-left.svg',
                                height: 12,
                                width: 12,
                                color: Colors.black,
                              ),
                              // IconButton(
                              //   icon: Icon(Icons.arrow_back, color: Colors.black),
                              //   onPressed: () {
                              //     Navigator.of(context).pop();
                              //   },
                              //),
                              SizedBox(width: 8),
                              Text(
                                'Previous page',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xff5D5D5D),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'TiemposText',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 24),

                      // Title
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _getTitle(provider),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                          ),
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: provider.clearText,
                              child: Text(
                                "Clear",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  color: Color(0xff0058FF),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 2),

                      // Main content area with icons and text field
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left side - Icons column

                            // SizedBox(width: 16),

                            // Right side - Large text field or table
                            Expanded(
                              child: _buildWorkflowContent(context, provider),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 8),

                      // Bottom buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Cancel button
                          TextButton(
                            onPressed: () {
                              // Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 10),
                              side: BorderSide(
                                  color: Color(0xffD0D0D0), width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontFamily: 'TiemposText',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),

                          SizedBox(width: 20),

                          // Previous button (show in entity and workflow creation steps)
                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation)
                            ElevatedButton(
                              onPressed: provider.goToPreviousStep,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade600,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              child: Text(
                                'Previous',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),

                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation)
                            SizedBox(width: 20),

                          // Main action button
                          ElevatedButton(
                            onPressed: _isValidating(provider)
                                ? null
                                : () => _handleButtonPress(provider),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isValidating(provider)
                                  ? Colors.grey.shade400
                                  : Color(0xff0058FF),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: _isValidating(provider)
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Validating...',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _getButtonText(provider),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontFamily: 'TiemposText',
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
                ),

                provider.showSidePanel
                    ? Container(
                        width: AppSpacing.spaceBetweenMainContentAndSidePanel)
                    : Expanded(child: SizedBox())
              ],
            ),
          ),
          // Side panel for role details
          if (provider.showSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: RoleDetailsPanel(
                role: provider.selectedRole!,
                onClose: () {
                  provider.hideSidePanel();
                },
                showLegacySections:
                    false, // Use new dedicated fields (CR/KPI/DA)
                users: provider
                    .extractedUsers, // Pass user data for role assignments
              ),
            )
        ],
      ),
    );
  }

  Widget rightSideTooltips(ManualCreationProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(right: 5.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // SizedBox(height: 20), // Align with text field

          // Agents icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/agent.svg',
            tooltipText: 'Agents',
            onTap: provider.handleAgentsTap,
          ),

          SizedBox(height: 10),

          // DataSets icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/cube-box.svg',
            tooltipText: 'DataSets',
            onTap: provider.handleDataSetsTap,
          ),

          SizedBox(height: 10),

          // Workflows icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/square-box-uncheck.svg',
            tooltipText: 'Workflows',
            onTap: provider.handleWorkflowsTap,
          ),
        ],
      ),
    );
  }

  // Show validation results in alert dialog
  void _showValidationAlertDialog(
      BuildContext context, ManualCreationProvider provider) {
    // Handle agent validation errors
    if (provider.validationError != null &&
        _lastShownError != provider.validationError) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(
          context, 'Agent Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
        provider.validationResult!.validationErrors != null &&
        provider.validationResult!.validationErrors!.isNotEmpty) {
      // Show agent validation errors in alert dialog
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages =
          errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(
            context, 'Agent Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
        provider.showAgentTable &&
        _lastShownResult != 'agent_success') {
      // Show success message when agent table is shown
      _lastShownResult = 'agent_success';
      _showSuccessAlertDialog(context, 'Agent Validation Successful',
          'Agent data loaded successfully.');
    }

    // Handle entity validation errors
    else if (provider.entityValidationError != null &&
        _lastShownError != provider.entityValidationError) {
      _lastShownError = provider.entityValidationError;
      _showErrorAlertDialog(
          context, 'Entity Validation Error', provider.entityValidationError!);
    } else if (provider.entityValidationResult != null &&
        provider.showEntityTable &&
        _lastShownResult != 'entity_success') {
      // Show success message when entity table is shown
      _lastShownResult = 'entity_success';
      _showSuccessAlertDialog(context, 'Entity Validation Successful',
          'Entity data loaded successfully.');
    }

    // Handle workflow validation errors
    else if (provider.workflowValidationError != null &&
        _lastShownError != provider.workflowValidationError) {
      _lastShownError = provider.workflowValidationError;
      _showErrorAlertDialog(context, 'Workflow Validation Error',
          provider.workflowValidationError!);
    } else if (provider.workflowValidationResult != null &&
        provider.showWorkflowTable &&
        _lastShownResult != 'workflow_success') {
      // Show success message when workflow table is shown
      _lastShownResult = 'workflow_success';
      _showSuccessAlertDialog(context, 'Workflow Validation Successful',
          'Workflow data loaded successfully.');
    }
  }

  // Show error alert dialog
  void _showErrorAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(
      BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined,
                        color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages
                        .map((message) => Padding(
                              padding: EdgeInsets.only(bottom: 8),
                              child: Text(
                                message,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  color: Colors.black87,
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.check_circle_outline,
                        color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper methods for workflow
  String _getTitle(ManualCreationProvider provider) {
    // If an icon view is active, show that title
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
        case 'DataSets':
          return provider.showEntityTable
              ? 'Entity List'
              : 'Create Your Entity';
        case 'Workflows':
          return provider.showWorkflowTable
              ? 'Workflow List'
              : 'Create Your Workflow';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? 'Workflow List'
            : 'Create Your Workflow';
    }
  }

  String _getButtonText(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Done' : 'Validate';
        case 'DataSets':
          return provider.showEntityTable ? 'Done' : 'Validate';
        case 'Workflows':
          return provider.showWorkflowTable ? 'Done' : 'Validate';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Next' : 'Validate';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Finish' : 'Validate';
    }
  }

  bool _isValidating(ManualCreationProvider provider) {
    return provider.isValidating ||
        provider.isValidatingEntity ||
        provider.isValidatingWorkflow;
  }

  void _handleButtonPress(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          if (provider.showAgentTable) {
            // Handle next action for agents (could navigate or finish)
            _showSuccessAlertDialog(context, 'Agents Complete',
                'Agent data processed successfully.');
          } else {
            // Call agent validation API
            provider.handleValidate();
          }
          break;
        case 'DataSets':
          if (provider.showEntityTable) {
            // Handle next action for entities (could navigate or finish)
            _showSuccessAlertDialog(context, 'Entities Complete',
                'Entity data processed successfully.');
          } else {
            // Call entity validation API
            provider.handleValidate();
          }
          break;
        case 'Workflows':
          if (provider.showWorkflowTable) {
            // Handle next action for workflows (could navigate or finish)
            _showSuccessAlertDialog(context, 'Workflows Complete',
                'Workflow data processed successfully.');
          } else {
            // Call workflow validation API
            provider.handleValidate();
          }
          break;
      }
      return;
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        if (provider.showAgentTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.entityCreation:
        if (provider.showEntityTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowCreation:
        if (provider.showWorkflowTable) {
          // Handle finish action (navigate away or complete workflow)
          _handleFinishWorkflow(provider);
        } else {
          provider.handleValidate();
        }
        break;
    }
  }

  void _handleFinishWorkflow(ManualCreationProvider provider) {
    // Show success message and navigate back or reset
    _showSuccessAlertDialog(context, 'Workflow Complete',
        'Agent, entity, and workflow creation completed successfully.');
    // Optionally reset the workflow or navigate away
    // provider.resetWorkflow();
  }

  Widget _buildWorkflowContent(
      BuildContext context, ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          // Show text field first, then table after validation
          return provider.showAgentTable
              ? _buildAgentTable(provider)
              : _buildTextField(context, provider);
        case 'DataSets':
          // Show text field first, then table after validation
          return provider.showEntityTable
              ? _buildEntityTable(provider)
              : _buildTextField(context, provider);
        case 'Workflows':
          // Show text field first, then table after validation
          return provider.showWorkflowTable
              ? _buildWorkflowTable(provider)
              : _buildTextField(context, provider);
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable
            ? _buildAgentTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.entityCreation:
        return provider.showEntityTable
            ? _buildEntityTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? _buildWorkflowTable(provider)
            : _buildTextField(context, provider);
    }
  }

  Widget _buildTextField(
      BuildContext context, ManualCreationProvider provider) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
        child: TextField(
          controller: provider.textController,
          maxLines: null,
          expands: true,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 16,
              fontFamily: 'TiemposText',
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildEntityTable(ManualCreationProvider provider) {
    if (provider.extractedEntityData == null ||
        provider.extractedEntityData!.entityGroups == null ||
        provider.extractedEntityData!.entityGroups!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No entity data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Entities and Relationships',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedEntityData!.entityGroups!.length} Entity Group${provider.extractedEntityData!.entityGroups!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Entity rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedEntityData!.entityGroups!.length,
              itemBuilder: (context, index) {
                final entityGroup =
                    provider.extractedEntityData!.entityGroups![index];

                return Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Entity group title
                      Text(
                        entityGroup.title ?? 'Unknown Entity',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 8),

                      // Entity count
                      if (entityGroup.entities != null &&
                          entityGroup.entities!.isNotEmpty)
                        Text(
                          '${entityGroup.entities!.length} entit${entityGroup.entities!.length != 1 ? 'ies' : 'y'} in this group',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTable(ManualCreationProvider provider) {
    final workflowData = provider.getLinearWorkflowData();

    if (workflowData == null) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No workflow data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    print('🔄 Rendering process flow diagram');

    // Use the new process flow diagram widget
    return _buildProcessFlowDiagram(workflowData);
  }

  Widget _buildProcessFlowDiagram(Map<String, dynamic> workflowData) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workflow content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main workflow title
                  _buildWorkflowTitle(workflowData),
                  SizedBox(height: 20),

                  // Process flow diagram
                  _buildProcessFlowNodes(workflowData),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTitle(Map<String, dynamic> workflowData) {
    final title = workflowData['title'] ?? '';

    // Only show title if it's not empty
    if (title.isEmpty) {
      return Text(
        'No workflow title available from API response',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'TiemposText',
          color: Colors.black,
          // fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
        fontFamily: 'TiemposText',
        color: Colors.black,
        // fontStyle: FontStyle.italic,
      ),
    );
  }

  // Build process flow nodes diagram
  Widget _buildProcessFlowNodes(Map<String, dynamic> workflowData) {
    final hierarchicalSteps =
        workflowData['hierarchicalSteps'] as List<Map<String, dynamic>>? ?? [];

    if (hierarchicalSteps.isEmpty) {
      return Text(
        'No process flow data available',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Extract process flow nodes from hierarchical data
    List<Map<String, dynamic>> processFlowNodes =
        _extractProcessFlowNodes(hierarchicalSteps);

    if (processFlowNodes.isEmpty) {
      return Text(
        'No process flow nodes found',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return _buildNodeConnectionDiagram(processFlowNodes);
  }

  // Extract process flow nodes from hierarchical data
  List<Map<String, dynamic>> _extractProcessFlowNodes(
      List<Map<String, dynamic>> hierarchicalSteps) {
    List<Map<String, dynamic>> nodes = [];

    for (var step in hierarchicalSteps) {
      if (step['children'] != null) {
        List<Map<String, dynamic>> children =
            List<Map<String, dynamic>>.from(step['children']);
        nodes.addAll(_extractNodesFromChildren(children));
      }
    }

    return nodes;
  }

  // Recursively extract nodes from children
  List<Map<String, dynamic>> _extractNodesFromChildren(
      List<Map<String, dynamic>> children) {
    List<Map<String, dynamic>> nodes = [];

    for (var child in children) {
      // Add the current node
      nodes.add(child);

      // Recursively add children
      if (child['children'] != null && (child['children'] as List).isNotEmpty) {
        List<Map<String, dynamic>> childNodes =
            List<Map<String, dynamic>>.from(child['children']);
        nodes.addAll(_extractNodesFromChildren(childNodes));
      }
    }

    return nodes;
  }

  // Build node connection diagram showing process flow
  Widget _buildNodeConnectionDiagram(List<Map<String, dynamic>> nodes) {
    if (nodes.isEmpty) {
      return Text(
        'No nodes to display',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Create a map of nodes by text/name for easy lookup (routes reference by text)
    Map<String, Map<String, dynamic>> nodeMap = {};
    for (var node in nodes) {
      String nodeKey = node['text'] ?? node['id'] ?? '';
      if (nodeKey.isNotEmpty) {
        nodeMap[nodeKey] = node;
      }
    }

    // Find root nodes (nodes that are not referenced by others)
    Set<String> referencedNodes = _findReferencedNodesByText(nodes);
    List<Map<String, dynamic>> rootNodes = nodes.where((node) {
      String nodeKey = node['text'] ?? node['id'] ?? '';
      return nodeKey.isNotEmpty && !referencedNodes.contains(nodeKey);
    }).toList();

    if (rootNodes.isEmpty && nodes.isNotEmpty) {
      // If no clear root nodes, use the first node as root
      rootNodes = [nodes.first];
    }

    // Step 1: Build complete tree with duplicates
    List<Widget> treeWidgets = [];
    Map<String, int> nodeOccurrences =
        {}; // Track node occurrences for deduplication

    for (int i = 0; i < rootNodes.length; i++) {
      if (i > 0) treeWidgets.add(SizedBox(width: 40));

      Widget treeWidget = _buildCompleteTreeWithDuplicates(
          rootNodes[i], nodeMap, <String>{}, 0, nodeOccurrences);
      treeWidgets.add(treeWidget);
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Container(
          padding: EdgeInsets.all(20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: treeWidgets,
          ),
        ),
      ),
    );
  }

  // Find all nodes that are referenced by other nodes (by text/name)
  Set<String> _findReferencedNodesByText(List<Map<String, dynamic>> nodes) {
    Set<String> referenced = {};

    for (var node in nodes) {
      // Check routes (these reference by text/name)
      if (node['routes'] != null) {
        List<String> routes = List<String>.from(node['routes']);
        referenced.addAll(routes);
      }

      // Check conditions (routeTo references by text/name)
      if (node['conditions'] != null) {
        List<dynamic> conditions = node['conditions'];
        for (var condition in conditions) {
          if (condition['routeTo'] != null) {
            referenced.add(condition['routeTo']);
          }
        }
      }

      // Check parallel routes (routeTo references by text/name)
      if (node['parallelRoutes'] != null) {
        List<dynamic> parallelRoutes = node['parallelRoutes'];
        for (var parallelRoute in parallelRoutes) {
          if (parallelRoute['routeTo'] != null) {
            referenced.add(parallelRoute['routeTo']);
          }
        }
      }
    }

    return referenced;
  }

  // Build complete tree with duplicates (Step 1)
  Widget _buildCompleteTreeWithDuplicates(
      Map<String, dynamic> node,
      Map<String, Map<String, dynamic>> nodeMap,
      Set<String> visited,
      int level,
      Map<String, int> nodeOccurrences,
      {Color? groupColor,
      int? alternateIndex,
      bool isParallel = false}) {
    String nodeKey = node['text'] ?? node['id'] ?? '';

    // Track node occurrences for deduplication
    nodeOccurrences[nodeKey] = (nodeOccurrences[nodeKey] ?? 0) + 1;
    int currentOccurrence = nodeOccurrences[nodeKey]!;

    // Prevent infinite loops in the same path
    if (visited.contains(nodeKey)) {
      return _buildDuplicateNodeReference(nodeKey, currentOccurrence);
    }
    visited.add(nodeKey);

    String routeType = node['routeType'] ?? '';
    String nodeName = node['text'] ?? '';

    // Get connected nodes
    List<String> connectedNodeKeys = _getConnectedNodeKeys(node);

    // Step 2: Check if this is a duplicate (not the first occurrence)
    if (currentOccurrence > 1) {
      return _buildDuplicateNodeReference(nodeKey, currentOccurrence);
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Current node - simplified display
        _buildSimplifiedNode(nodeName, groupColor, alternateIndex, isParallel),

        // Connection lines and child nodes
        if (connectedNodeKeys.isNotEmpty) ...[
          SizedBox(width: 16),
          _buildHorizontalConnectionsWithDeduplication(
              node, nodeMap, visited, level + 1, nodeOccurrences),
        ] else ...[
          // Add terminal icon at the end of workflow paths
          SizedBox(width: 16),
          _buildTerminalIcon(),
        ],
      ],
    );
  }

  // Build duplicate node reference (Step 3)
  Widget _buildDuplicateNodeReference(String nodeKey, int occurrence) {
    return Container();

    // Container(
    //   padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    //   decoration: BoxDecoration(
    //     color: Colors.grey.shade50,
    //     border: Border.all(color: Colors.grey.shade300, width: 1),
    //     borderRadius: BorderRadius.circular(6),
    //     boxShadow: [
    //       BoxShadow(
    //         color: Colors.grey.shade200,
    //         blurRadius: 2,
    //         offset: Offset(1, 1),
    //       ),
    //     ],
    //   ),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     children: [
    //       Icon(
    //         Icons.call_made,
    //         size: 16,
    //         color: Colors.blue.shade600,
    //       ),
    //       SizedBox(width: 6),
    //       Text(
    //         '→ Continues from "$nodeKey" above',
    //         style: TextStyle(
    //           fontSize: 12,
    //           fontFamily: 'TiemposText',
    //           color: Colors.blue.shade700,
    //           fontStyle: FontStyle.italic,
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }

  // Build node box widget
  Widget _buildNodeBox(String nodeName, String routeType, String actorType) {
    Color nodeColor = _getNodeColorByRouteType(routeType);
    Color borderColor = _getNodeBorderColorByRouteType(routeType);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: nodeColor,
        border: Border.all(color: borderColor, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            nodeName,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),
          if (actorType.isNotEmpty) ...[
            SizedBox(height: 4),
            Text(
              'Actor: $actorType',
              style: TextStyle(
                fontSize: 12,
                fontFamily: 'TiemposText',
                color: Colors.grey.shade700,
              ),
            ),
          ],
          if (routeType.isNotEmpty) ...[
            SizedBox(height: 4),
            Text(
              'Type: $routeType',
              style: TextStyle(
                fontSize: 12,
                fontFamily: 'TiemposText',
                color: Colors.grey.shade700,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Build simplified node display - only LO name as plain text
  Widget _buildSimplifiedNode(String nodeName, Color? groupColor,
      int? alternateIndex, bool isParallel) {
    // For alternate routes, display as "alt1", "alt2", etc.
    String displayText = nodeName;
    if (alternateIndex != null) {
      displayText = 'alt$alternateIndex';
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: groupColor != null
          ? BoxDecoration(
              color: groupColor,
              borderRadius: BorderRadius.circular(4),
            )
          : null,
      child: Text(
        displayText,
        style: TextStyle(
          fontSize: 14,
          fontFamily: 'TiemposText',
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Build terminal icon for workflow end
  Widget _buildTerminalIcon() {
    return SvgPicture.asset(
      'assets/images/workflow/terminal_icon.svg',
      width: 16,
      height: 16,
      colorFilter: ColorFilter.mode(Colors.red.shade600, BlendMode.srcIn),
    );
  }

  // Build link icon for connections
  Widget _buildLinkIcon() {
    return SvgPicture.asset(
      'assets/images/workflow/link_icon.svg',
      width: 16,
      height: 16,
      colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
    );
  }

  // Get node color by route type
  Color _getNodeColorByRouteType(String routeType) {
    switch (routeType.toLowerCase()) {
      case 'sequential':
        return Colors.green.shade50;
      case 'alternate':
        return Colors.blue.shade50;
      case 'parallel':
        return Colors.orange.shade50;
      default:
        return Colors.grey.shade50;
    }
  }

  // Get node border color by route type
  Color _getNodeBorderColorByRouteType(String routeType) {
    switch (routeType.toLowerCase()) {
      case 'sequential':
        return Colors.green.shade400;
      case 'alternate':
        return Colors.blue.shade400;
      case 'parallel':
        return Colors.orange.shade400;
      default:
        return Colors.grey.shade400;
    }
  }

  // Get all connected node keys from a node
  List<String> _getConnectedNodeKeys(Map<String, dynamic> node) {
    List<String> connectedKeys = [];

    // Add routes
    if (node['routes'] != null) {
      List<String> routes = List<String>.from(node['routes']);
      connectedKeys.addAll(routes);
    }

    // Add condition routes
    if (node['conditions'] != null) {
      List<dynamic> conditions = node['conditions'];
      for (var condition in conditions) {
        if (condition['routeTo'] != null) {
          connectedKeys.add(condition['routeTo']);
        }
      }
    }

    // Add parallel routes
    if (node['parallelRoutes'] != null) {
      List<dynamic> parallelRoutes = node['parallelRoutes'];
      for (var parallelRoute in parallelRoutes) {
        if (parallelRoute['routeTo'] != null) {
          connectedKeys.add(parallelRoute['routeTo']);
        }
      }
    }

    return connectedKeys;
  }

  // Build horizontal connections with proper visual flow
  Widget _buildHorizontalConnectionsWithDeduplication(
      Map<String, dynamic> node,
      Map<String, Map<String, dynamic>> nodeMap,
      Set<String> visited,
      int level,
      Map<String, int> nodeOccurrences) {
    String routeType = node['routeType'] ?? '';
    List<String> connectedNodeKeys = _getConnectedNodeKeys(node);

    if (connectedNodeKeys.isEmpty) {
      return Container();
    }

    // Filter out nodes that don't exist in nodeMap
    List<String> validNodeKeys =
        connectedNodeKeys.where((key) => nodeMap.containsKey(key)).toList();

    if (validNodeKeys.isEmpty) {
      return Container();
    }

    switch (routeType.toLowerCase()) {
      case 'sequential':
        return _buildSequentialHorizontalFlow(
            validNodeKeys, nodeMap, visited, level, nodeOccurrences);
      case 'alternate':
        return _buildAlternateHorizontalFlow(
            node, validNodeKeys, nodeMap, visited, level, nodeOccurrences);
      case 'parallel':
        return _buildParallelHorizontalFlow(
            node, validNodeKeys, nodeMap, visited, level, nodeOccurrences);
      default:
        return _buildSequentialHorizontalFlow(
            validNodeKeys, nodeMap, visited, level, nodeOccurrences);
    }
  }

  // Build sequential horizontal flow: Node A ——→ Node B
  Widget _buildSequentialHorizontalFlow(
      List<String> nodeKeys,
      Map<String, Map<String, dynamic>> nodeMap,
      Set<String> visited,
      int level,
      Map<String, int> nodeOccurrences) {
    if (nodeKeys.isEmpty) return Container();

    List<Widget> flowWidgets = [];

    for (int i = 0; i < nodeKeys.length; i++) {
      if (i > 0) {
        // Add simple spacing between sequential nodes
        flowWidgets.add(SizedBox(width: 16));
      }

      // Add the next node with simplified styling
      flowWidgets.add(
        _buildCompleteTreeWithDuplicates(nodeMap[nodeKeys[i]]!, nodeMap,
            Set.from(visited), level, nodeOccurrences),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: flowWidgets,
    );
  }

  // Build alternate horizontal flow: Node A branches to multiple nodes with conditions
  Widget _buildAlternateHorizontalFlow(
      Map<String, dynamic> node,
      List<String> nodeKeys,
      Map<String, Map<String, dynamic>> nodeMap,
      Set<String> visited,
      int level,
      Map<String, int> nodeOccurrences) {
    List<dynamic> conditions = node['conditions'] ?? [];

    if (nodeKeys.isEmpty) return Container();

    // Get pastel color for this alternate group
    final provider =
        Provider.of<ManualCreationProvider>(context, listen: false);
    Color groupColor =
        provider.pastelColors[level % provider.pastelColors.length];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < nodeKeys.length && i < conditions.length; i++) ...[
          if (i > 0) SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Link icon
              _buildLinkIcon(),
              SizedBox(width: 8),
              // Connected node with alternate numbering and group color
              _buildCompleteTreeWithDuplicates(
                nodeMap[nodeKeys[i]]!,
                nodeMap,
                Set.from(visited),
                level,
                nodeOccurrences,
                groupColor: groupColor,
                alternateIndex: i + 1,
              ),
            ],
          ),
        ],
      ],
    );
  }

  // Build parallel horizontal flow: Multiple parallel arrows with optional join
  Widget _buildParallelHorizontalFlow(
      Map<String, dynamic> node,
      List<String> nodeKeys,
      Map<String, Map<String, dynamic>> nodeMap,
      Set<String> visited,
      int level,
      Map<String, int> nodeOccurrences) {
    List<dynamic> parallelRoutes = node['parallelRoutes'] ?? [];
    String joinAt = node['joinAt'] ?? '';

    if (nodeKeys.isEmpty) return Container();

    // Get pastel color for this parallel group
    final provider =
        Provider.of<ManualCreationProvider>(context, listen: false);
    Color groupColor =
        provider.pastelColors[(level + 1) % provider.pastelColors.length];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0;
            i < nodeKeys.length && i < parallelRoutes.length;
            i++) ...[
          if (i > 0) SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Connected node with parallel group color
              _buildCompleteTreeWithDuplicates(
                nodeMap[nodeKeys[i]]!,
                nodeMap,
                Set.from(visited),
                level,
                nodeOccurrences,
                groupColor: groupColor,
                isParallel: true,
              ),
            ],
          ),
        ],

        // Add join point if specified
        if (joinAt.isNotEmpty && nodeMap.containsKey(joinAt)) ...[
          SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildCompleteTreeWithDuplicates(nodeMap[joinAt]!, nodeMap,
                  Set.from(visited), level, nodeOccurrences),
            ],
          ),
        ],
      ],
    );
  }

  // Build agent table similar to web_home_screen_chat.dart
  Widget _buildAgentTable(ManualCreationProvider provider) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
                vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
            decoration: BoxDecoration(
              // color: Colors.grey.shade50,
              border: Border.all(color: Color(0xffD0D0D0)),
            ),
            child: Row(
              children: [
                Text(
                  'Roles',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    color: Color(0xffFFE5B4),
                    borderRadius: BorderRadius.circular(AppSpacing.xxs),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate:
                      _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  coreResponsibilities: agent.sections
                      .where((section) => section.title
                          .toLowerCase()
                          .contains('responsibilities'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                  // Extract permissions from agent sections
                  kpis: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('performance'))
                      .expand((section) => section.items)
                      .toList(),
                  decisionAuthority: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('authority'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                );

                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                    ),
                  ),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: provider.selectedRole?.id == role.id,
                    isBorderRequired: false,
                    isHoverCardRequired: false,
                    selectedColor: Color(0xffF2F2F2),
                    onRoleTap: (selectedRole) {
                      provider.showRoleDetailsPanel(selectedRole);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return _IconWithTooltip(
      iconPath: iconPath,
      tooltipText: tooltipText,
      isSelected:
          _activeIconView == tooltipText, // Check if this icon is selected
      onTap: () {
        // Set the active icon view
        setState(() {
          _activeIconView = tooltipText;
        });
        // Call the original onTap if needed
        onTap();
      },
    );
  }
}

class _IconWithTooltip extends StatefulWidget {
  final String iconPath;
  final String tooltipText;
  final VoidCallback onTap;
  final bool isSelected;

  const _IconWithTooltip({
    required this.iconPath,
    required this.tooltipText,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_IconWithTooltip> createState() => _IconWithTooltipState();
}

// Custom painter for dotted vertical lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    this.dashLength = 5.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for dotted horizontal lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    this.dashLength = 3.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _IconWithTooltipState extends State<_IconWithTooltip> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width + 8, // 8px gap from icon
          top: position.dy + (size.height / 2) - 18, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xxs,
              ),
              child: Text(
                widget.tooltipText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      print('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the icon color based on selected and hover states
    Color iconColor;
    if (widget.isSelected) {
      iconColor = Color(0xff0058FF); // Blue when selected
    } else if (isHovered) {
      iconColor = Color(0xff0058FF); // Blue when hovered
    } else {
      iconColor = Color(0xff797676); // Default gray
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() => isHovered = true);
        _showTooltip();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _removeTooltip();
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          key: _iconKey,
          padding: EdgeInsets.all(4), // Add padding for background
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Color(0xff0058FF)
                    .withOpacity(0.1) // Light blue background when selected
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: SvgPicture.asset(
            widget.iconPath,
            width: 11,
            height: 11,
            color: iconColor,
            // Add stroke width for bold effect when selected
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
