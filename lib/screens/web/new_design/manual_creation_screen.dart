import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../../providers/manual_creation_provider.dart';
import '../../../models/role_info.dart';
import '../../../widgets/resizable_panel.dart';
import 'widgets/chat_widgets/build_role_card.dart';
import 'widgets/role_details_panel.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _ManualCreationScreenContent();
  }
}

class _ManualCreationScreenContent extends StatefulWidget {
  @override
  _ManualCreationScreenContentState createState() =>
      _ManualCreationScreenContentState();
}

class _ManualCreationScreenContentState
    extends State<_ManualCreationScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;
  String? _activeIconView = "Agents"; // Track which icon view is active

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(
      BuildContext context, ManualCreationProvider provider) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                provider.showSidePanel
                    ? SizedBox(
                        width: AppSpacing.spaceBetweenMainContentAndSidePanel,
                        child: rightSideTooltips(provider),
                      )
                    : Expanded(
                        child: rightSideTooltips(provider),
                      ),

                // Main content area
                Expanded(
                  flex: provider.showSidePanel ? 3 : 7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: AppSpacing.sm),
                      // Header with back button
                      GestureDetector(
                        onTap: () {
                          Provider.of<WebHomeProvider>(context, listen: false)
                              .currentScreenIndex = ScreenConstants.home;
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/arrow-left.svg',
                                height: 12,
                                width: 12,
                                color: Colors.black,
                              ),
                              // IconButton(
                              //   icon: Icon(Icons.arrow_back, color: Colors.black),
                              //   onPressed: () {
                              //     Navigator.of(context).pop();
                              //   },
                              //),
                              SizedBox(width: 8),
                              Text(
                                'Previous page',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xff5D5D5D),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'TiemposText',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 24),

                      // Title
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _getTitle(provider),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                          ),
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: provider.clearText,
                              child: Text(
                                "Clear",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  color: Color(0xff0058FF),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 2),

                      // Main content area with icons and text field
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left side - Icons column

                            // SizedBox(width: 16),

                            // Right side - Large text field or table
                            Expanded(
                              child: _buildWorkflowContent(context, provider),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 8),

                      // Bottom buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Cancel button
                          TextButton(
                            onPressed: () {
                              // Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 10),
                              side: BorderSide(
                                  color: Color(0xffD0D0D0), width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontFamily: 'TiemposText',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),

                          SizedBox(width: 20),

                          // Previous button (show in entity and workflow creation steps)
                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation)
                            ElevatedButton(
                              onPressed: provider.goToPreviousStep,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade600,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              child: Text(
                                'Previous',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),

                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation)
                            SizedBox(width: 20),

                          // Main action button
                          ElevatedButton(
                            onPressed: _isValidating(provider)
                                ? null
                                : () => _handleButtonPress(provider),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isValidating(provider)
                                  ? Colors.grey.shade400
                                  : Color(0xff0058FF),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: _isValidating(provider)
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Validating...',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _getButtonText(provider),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontFamily: 'TiemposText',
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                          ),

                          // Test Process Flow button (only for workflows)
                          if ((_activeIconView == 'Workflows' ||
                                  provider.currentStep ==
                                      WorkflowStep.workflowCreation) &&
                              !_isValidating(provider)) ...[
                            SizedBox(width: 12),
                            ElevatedButton(
                              onPressed: () =>
                                  provider.testProcessFlowParsing(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              child: Text(
                                'Test Process Flow',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
                ),

                provider.showSidePanel
                    ? Container(
                        width: AppSpacing.spaceBetweenMainContentAndSidePanel)
                    : Expanded(child: SizedBox())
              ],
            ),
          ),
          // Side panel for role details
          if (provider.showSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: RoleDetailsPanel(
                role: provider.selectedRole!,
                onClose: () {
                  provider.hideSidePanel();
                },
                showLegacySections:
                    false, // Use new dedicated fields (CR/KPI/DA)
                users: provider
                    .extractedUsers, // Pass user data for role assignments
              ),
            )
        ],
      ),
    );
  }

  Widget rightSideTooltips(ManualCreationProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(right: 5.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // SizedBox(height: 20), // Align with text field

          // Agents icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/agent.svg',
            tooltipText: 'Agents',
            onTap: provider.handleAgentsTap,
          ),

          SizedBox(height: 10),

          // DataSets icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/cube-box.svg',
            tooltipText: 'DataSets',
            onTap: provider.handleDataSetsTap,
          ),

          SizedBox(height: 10),

          // Workflows icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/square-box-uncheck.svg',
            tooltipText: 'Workflows',
            onTap: provider.handleWorkflowsTap,
          ),
        ],
      ),
    );
  }

  // Show validation results in alert dialog
  void _showValidationAlertDialog(
      BuildContext context, ManualCreationProvider provider) {
    // Handle agent validation errors
    if (provider.validationError != null &&
        _lastShownError != provider.validationError) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(
          context, 'Agent Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
        provider.validationResult!.validationErrors != null &&
        provider.validationResult!.validationErrors!.isNotEmpty) {
      // Show agent validation errors in alert dialog
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages =
          errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(
            context, 'Agent Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
        provider.showAgentTable &&
        _lastShownResult != 'agent_success') {
      // Show success message when agent table is shown
      _lastShownResult = 'agent_success';
      _showSuccessAlertDialog(context, 'Agent Validation Successful',
          'Agent data loaded successfully.');
    }

    // Handle entity validation errors
    else if (provider.entityValidationError != null &&
        _lastShownError != provider.entityValidationError) {
      _lastShownError = provider.entityValidationError;
      _showErrorAlertDialog(
          context, 'Entity Validation Error', provider.entityValidationError!);
    } else if (provider.entityValidationResult != null &&
        provider.showEntityTable &&
        _lastShownResult != 'entity_success') {
      // Show success message when entity table is shown
      _lastShownResult = 'entity_success';
      _showSuccessAlertDialog(context, 'Entity Validation Successful',
          'Entity data loaded successfully.');
    }

    // Handle workflow validation errors
    else if (provider.workflowValidationError != null &&
        _lastShownError != provider.workflowValidationError) {
      _lastShownError = provider.workflowValidationError;
      _showErrorAlertDialog(context, 'Workflow Validation Error',
          provider.workflowValidationError!);
    } else if (provider.workflowValidationResult != null &&
        provider.showWorkflowTable &&
        _lastShownResult != 'workflow_success') {
      // Show success message when workflow table is shown
      _lastShownResult = 'workflow_success';
      _showSuccessAlertDialog(context, 'Workflow Validation Successful',
          'Workflow data loaded successfully.');
    }
  }

  // Show error alert dialog
  void _showErrorAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(
      BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined,
                        color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages
                        .map((message) => Padding(
                              padding: EdgeInsets.only(bottom: 8),
                              child: Text(
                                message,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  color: Colors.black87,
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.check_circle_outline,
                        color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper methods for workflow
  String _getTitle(ManualCreationProvider provider) {
    // If an icon view is active, show that title
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
        case 'DataSets':
          return provider.showEntityTable
              ? 'Entity List'
              : 'Create Your Entity';
        case 'Workflows':
          return provider.showWorkflowTable
              ? 'Workflow List'
              : 'Create Your Workflow';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? 'Workflow List'
            : 'Create Your Workflow';
    }
  }

  String _getButtonText(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Done' : 'Validate';
        case 'DataSets':
          return provider.showEntityTable ? 'Done' : 'Validate';
        case 'Workflows':
          return provider.showWorkflowTable ? 'Done' : 'Validate';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Next' : 'Validate';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Finish' : 'Validate';
    }
  }

  bool _isValidating(ManualCreationProvider provider) {
    return provider.isValidating ||
        provider.isValidatingEntity ||
        provider.isValidatingWorkflow;
  }

  void _handleButtonPress(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          if (provider.showAgentTable) {
            // Handle next action for agents (could navigate or finish)
            _showSuccessAlertDialog(context, 'Agents Complete',
                'Agent data processed successfully.');
          } else {
            // Call agent validation API
            provider.handleValidate();
          }
          break;
        case 'DataSets':
          if (provider.showEntityTable) {
            // Handle next action for entities (could navigate or finish)
            _showSuccessAlertDialog(context, 'Entities Complete',
                'Entity data processed successfully.');
          } else {
            // Call entity validation API
            provider.handleValidate();
          }
          break;
        case 'Workflows':
          if (provider.showWorkflowTable) {
            // Handle next action for workflows (could navigate or finish)
            _showSuccessAlertDialog(context, 'Workflows Complete',
                'Workflow data processed successfully.');
          } else {
            // Call workflow validation API
            provider.handleValidate();
          }
          break;
      }
      return;
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        if (provider.showAgentTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.entityCreation:
        if (provider.showEntityTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowCreation:
        if (provider.showWorkflowTable) {
          // Handle finish action (navigate away or complete workflow)
          _handleFinishWorkflow(provider);
        } else {
          provider.handleValidate();
        }
        break;
    }
  }

  void _handleFinishWorkflow(ManualCreationProvider provider) {
    // Show success message and navigate back or reset
    _showSuccessAlertDialog(context, 'Workflow Complete',
        'Agent, entity, and workflow creation completed successfully.');
    // Optionally reset the workflow or navigate away
    // provider.resetWorkflow();
  }

  Widget _buildWorkflowContent(
      BuildContext context, ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          // Show text field first, then table after validation
          return provider.showAgentTable
              ? _buildAgentTable(provider)
              : _buildTextField(context, provider);
        case 'DataSets':
          // Show text field first, then table after validation
          return provider.showEntityTable
              ? _buildEntityTable(provider)
              : _buildTextField(context, provider);
        case 'Workflows':
          // Show text field first, then table after validation
          return provider.showWorkflowTable
              ? _buildWorkflowTable(provider)
              : _buildTextField(context, provider);
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable
            ? _buildAgentTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.entityCreation:
        return provider.showEntityTable
            ? _buildEntityTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? _buildWorkflowTable(provider)
            : _buildTextField(context, provider);
    }
  }

  Widget _buildTextField(
      BuildContext context, ManualCreationProvider provider) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
        child: TextField(
          controller: provider.textController,
          maxLines: null,
          expands: true,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 16,
              fontFamily: 'TiemposText',
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildEntityTable(ManualCreationProvider provider) {
    if (provider.extractedEntityData == null ||
        provider.extractedEntityData!.entityGroups == null ||
        provider.extractedEntityData!.entityGroups!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No entity data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Entities and Relationships',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedEntityData!.entityGroups!.length} Entity Group${provider.extractedEntityData!.entityGroups!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Entity rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedEntityData!.entityGroups!.length,
              itemBuilder: (context, index) {
                final entityGroup =
                    provider.extractedEntityData!.entityGroups![index];

                return Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Entity group title
                      Text(
                        entityGroup.title ?? 'Unknown Entity',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 8),

                      // Entity count
                      if (entityGroup.entities != null &&
                          entityGroup.entities!.isNotEmpty)
                        Text(
                          '${entityGroup.entities!.length} entit${entityGroup.entities!.length != 1 ? 'ies' : 'y'} in this group',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTable(ManualCreationProvider provider) {
    final workflowData = provider.getLinearWorkflowData();

    if (workflowData == null) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No workflow data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    print('🌳 Rendering linear workflow tree');

    // Use the linear tree widget to display workflow data
    return _buildLinearWorkflowTree(workflowData);
  }

  Widget _buildLinearWorkflowTree(Map<String, dynamic> workflowData) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workflow content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main workflow title
                  _buildWorkflowTitle(workflowData),
                  SizedBox(height: 5),

                  // Workflow subtitle
                  // _buildWorkflowSubtitle(workflowData),
                  // SizedBox(height: 16),

                  // // Sequential steps in one line
                  // _buildSequentialSteps(workflowData),
                  SizedBox(height: 18),

                  // Hierarchical breakdown
                  _buildHierarchicalBreakdown(workflowData),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTitle(Map<String, dynamic> workflowData) {
    final title = workflowData['title'] ?? '';

    // Only show title if it's not empty
    if (title.isEmpty) {
      return Text(
        'No workflow title available from API response',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'TiemposText',
          color: Colors.black,
          // fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
        fontFamily: 'TiemposText',
        color: Colors.black,
        // fontStyle: FontStyle.italic,
      ),
    );
  }

  Widget _buildWorkflowSubtitle(Map<String, dynamic> workflowData) {
    final subtitle = workflowData['subtitle'] ?? '';

    // Only show subtitle if it's not empty
    if (subtitle.isEmpty) {
      return SizedBox.shrink();
    }

    return Text(
      subtitle,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        fontFamily: 'TiemposText',
        color: Colors.black,
        // fontStyle: FontStyle.italic,
      ),
    );
  }

  Widget _buildSequentialSteps(Map<String, dynamic> workflowData) {
    final steps = workflowData['sequentialSteps'] as List<String>? ?? [];

    if (steps.isEmpty) {
      return Text(
        'No workflow steps available from API response',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      steps.join(', '),
      style: TextStyle(
        fontSize: 12,
        fontFamily: 'TiemposText',
        color: Colors.black87,
      ),
    );
  }

  Widget _buildHierarchicalBreakdown(Map<String, dynamic> workflowData) {
    final hierarchicalSteps =
        workflowData['hierarchicalSteps'] as List<Map<String, dynamic>>? ?? [];

    if (hierarchicalSteps.isEmpty) {
      return Text(
        'No hierarchical workflow structure available from API response',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Build tree structure similar to WorkflowTreeBuilder
    return _buildWorkflowTreeStructure(hierarchicalSteps);
  }

  // Build workflow tree structure with connecting lines
  Widget _buildWorkflowTreeStructure(
      List<Map<String, dynamic>> hierarchicalSteps) {
    if (hierarchicalSteps.isEmpty) return SizedBox.shrink();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Stack(
        children: [
          // Main vertical connector line
          Positioned(
            left: 3,
            top: 15,
            bottom: 80,
            child: CustomPaint(
              size: Size(1, double.infinity),
              painter: DottedLinePainter(
                color: Colors.grey.shade400,
                dashLength: 3,
                dashGap: 3,
              ),
            ),
          ),

          // Main workflow content
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Build each root level node (GO)
              for (int i = 0; i < hierarchicalSteps.length; i++) ...[
                if (i > 0) SizedBox(height: 16),
                _buildTreeNode(hierarchicalSteps[i], isRoot: true),
              ],
            ],
          ),
        ],
      ),
    );
  }

  // Build individual tree node with proper styling and connections
  Widget _buildTreeNode(Map<String, dynamic> node, {bool isRoot = false}) {
    final text = node['text'] as String? ?? '';
    final children = node['children'] as List<Map<String, dynamic>>? ?? [];
    final flowType = node['flowType'] as String? ?? '';
    final actorType = node['actorType'] as String? ?? '';
    final conditionText = node['conditionText'] as String? ?? '';
    final parallelDescription = node['parallelDescription'] as String? ?? '';

    // Determine node color based on flow type
    Color nodeColor = _getNodeColor(flowType);
    Color borderColor = _getNodeBorderColor(flowType);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Node row
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Circle indicator
            Container(
              margin: EdgeInsets.only(right: 8),
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: borderColor, width: 0.5),
                color: nodeColor,
              ),
            ),

            // Node text with additional info
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main node text
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
            
                // Additional info based on node type
                if (conditionText.isNotEmpty) ...[
                  SizedBox(height: 2),
                  Text(
                    'Condition: $conditionText',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'TiemposText',
                      color: Colors.blue.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
            
                if (parallelDescription.isNotEmpty) ...[
                  SizedBox(height: 2),
                  Text(
                    'Parallel: $parallelDescription',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'TiemposText',
                      color: Colors.orange.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
            
                if (actorType.isNotEmpty && !isRoot) ...[
                  SizedBox(height: 2),
                  Text(
                    'Actor: $actorType',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),

        // Build children (process flow nodes)
        if (children.isNotEmpty) ...[
          SizedBox(height: 16),
          _buildChildrenNodes(children),
        ],
      ],
    );
  }

  // Helper method to get node color based on flow type
  Color _getNodeColor(String flowType) {
    switch (flowType) {
      case 'conditional':
        return Colors.blue.shade100;
      case 'parallel':
      case 'parallel_item':
        return Colors.orange.shade100;
      case 'sequential':
        return Colors.green.shade100;
      default:
        return Colors.white;
    }
  }

  // Helper method to get node border color based on flow type
  Color _getNodeBorderColor(String flowType) {
    switch (flowType) {
      case 'conditional':
        return Colors.blue.shade400;
      case 'parallel':
      case 'parallel_item':
        return Colors.orange.shade400;
      case 'sequential':
        return Colors.green.shade400;
      default:
        return Colors.grey.shade400;
    }
  }

  // Build children nodes (pathways and LOs) with proper indentation and connections
  Widget _buildChildrenNodes(List<Map<String, dynamic>> children) {
    return Padding(
      padding: EdgeInsets.only(left: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildChildrenNodesList(children),
      ),
    );
  }

  // Helper method to build children nodes list with proper parallel grouping
  List<Widget> _buildChildrenNodesList(List<Map<String, dynamic>> children) {
    List<Widget> widgets = [];

    for (int i = 0; i < children.length; i++) {
      if (i > 0) widgets.add(SizedBox(height: 16));

      // Check if this is a parallel item and group consecutive parallel items
      if (children[i]['flowType'] == 'parallel_item') {
        // Find all consecutive parallel items starting from current index
        widgets.add(_buildParallelGroup(children, i));

        // Skip the parallel items we just processed
        while (i + 1 < children.length &&
            children[i + 1]['flowType'] == 'parallel_item') {
          i++; // Skip the next parallel item
        }
      } else {
        // Regular child node with horizontal connector
        widgets.add(Stack(
          children: [
            // Vertical connector for this child's children
            if (children[i]['children'] != null &&
                (children[i]['children'] as List).isNotEmpty)
              Positioned(
                left: 50,
                top: 16,
                bottom: 37,
                child: CustomPaint(
                  size: Size(1, double.infinity),
                  painter: DottedLinePainter(
                    color: Colors.grey.shade400,
                    dashLength: 3,
                    dashGap: 3,
                  ),
                ),
              ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Child node row
                Stack(
                  children: [
                    Positioned(
                      left: -15, // Start from main vertical line position
                      top: 11,
                      width: 65, // Length of horizontal connector
                      height: 1,
                      child: CustomPaint(
                        size: Size(90, 1),
                        painter: HorizontalDottedLinePainter(
                          color: Colors.grey.shade400,
                          dashLength: 3,
                          dashGap: 3,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Horizontal connector and circle
                        Row(
                          children: [
                            // Circle indicator
                            SizedBox(width: 47),

                            Container(
                              margin: EdgeInsets.only(right: 8, top: 3),
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.grey.shade400, width: 0.5),
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),

                        // Node text
                        Text(
                          children[i]['text'] ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'TiemposText',
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Nested children (LOs)
                if (children[i]['children'] != null &&
                    (children[i]['children'] as List).isNotEmpty) ...[
                  SizedBox(height: 16),
                  _buildNestedLOs(children[i]['children'] as List),
                ],
              ],
            ),
          ],
        ));
      }
    }

    return widgets;
  }

  // Build parallel group of LOs - each LO on its own line vertically
  Widget _buildParallelGroup(
      List<Map<String, dynamic>> children, int startIndex) {
    // Find all consecutive parallel items
    List<Map<String, dynamic>> parallelItems = [];
    for (int i = startIndex;
        i < children.length && children[i]['flowType'] == 'parallel_item';
        i++) {
      parallelItems.add(children[i]);
    }

    // Build each parallel LO as a separate row (vertically stacked)
    List<Widget> parallelWidgets = [];

    for (int i = 0; i < parallelItems.length; i++) {
      if (i > 0) parallelWidgets.add(SizedBox(height: 16));

      parallelWidgets.add(
        Stack(
          children: [
            // Vertical connector for continuation (if not the last item)
            if (i < parallelItems.length - 1)
              Positioned(
                left: 18,
                top: 16,
                bottom: 0,
                child: CustomPaint(
                  size: Size(1, double.infinity),
                  painter: DottedLinePainter(
                    color: Colors.orange.shade300,
                    dashLength: 3,
                    dashGap: 3,
                  ),
                ),
              ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Parallel LO row
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Horizontal connector and circle
                    Stack(
                      children: [
                        // Horizontal dotted connector
                        Positioned(
                          left: 3,
                          top: 11,
                          width: 12,
                          height: 1,
                          child: CustomPaint(
                            size: Size(44, 1),
                            painter: HorizontalDottedLinePainter(
                              color: Colors.orange.shade300,
                              dashLength: 3,
                              dashGap: 3,
                            ),
                          ),
                        ),

                        Row(
                          children: [
                            SizedBox(width: 15),

                            // Circle indicator for parallel item
                            Container(
                              margin: EdgeInsets.only(right: 8, top: 3),
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.orange.shade400, width: 0.5),
                                color: Colors.orange.shade100,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    // Parallel LO text with special styling
                    Text(
                      parallelItems[i]['text'] ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'TiemposText',
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: parallelWidgets,
    );
  }

  // Build nested LOs with proper indentation and series/parallel flow handling
  Widget _buildNestedLOs(List<dynamic> children) {
    // Convert children to Map<String, dynamic> for easier handling
    final List<Map<String, dynamic>> childNodes =
        children.map((child) => Map<String, dynamic>.from(child)).toList();

    return Padding(
      padding: EdgeInsets.only(left: 48),
      child: _buildSequentialLOsLayout(childNodes),
    );
  }

  // Build the layout for nested LOs following sequential order
  Widget _buildSequentialLOsLayout(List<Map<String, dynamic>> childNodes) {
    List<Widget> rowItems = [];
    bool isFirstSeriesLO =
        true; // Track if this is the first series LO in the pathway

    for (int i = 0; i < childNodes.length; i++) {
      if (i > 0) rowItems.add(SizedBox(width: 16));

      // Check if this is a parallel item and group consecutive parallel items
      if (childNodes[i]['flowType'] == 'parallel_item') {
        // Find all consecutive parallel items starting from current index
        List<Map<String, dynamic>> parallelGroup = [];
        int j = i;
        while (j < childNodes.length &&
            childNodes[j]['flowType'] == 'parallel_item') {
          parallelGroup.add(childNodes[j]);
          j++;
        }

        // Add parallel group as a column in the row
        rowItems.add(_buildParallelGroupInRow(parallelGroup));

        // Skip the parallel items we just processed
        i = j - 1; // -1 because the for loop will increment i

        // After parallel items, the next series LO should not be considered first
        isFirstSeriesLO = false;
      } else {
        // Regular series LO - only first one gets circle indicator
        rowItems.add(_buildSingleSeriesLO(childNodes[i],
            isFirstInPathway: isFirstSeriesLO));
        isFirstSeriesLO = false; // After first series LO, set to false
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rowItems,
    );
  }

  // Build a single series LO
  Widget _buildSingleSeriesLO(Map<String, dynamic> childNode,
      {bool isFirstInPathway = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Horizontal connector and circle
        Stack(
          children: [
            Row(
              children: [
                if (isFirstInPathway)
                  Positioned(
                    left: 0, // Start from main vertical line position
                    top: 12,
                    width: 30, // Length of horizontal connector
                    height: 1,
                    child: CustomPaint(
                      size: Size(30, 1),
                      painter: HorizontalDottedLinePainter(
                        color: Colors.grey.shade400,
                        dashLength: 3,
                        dashGap: 3,
                      ),
                    ),
                  ),
                // Circle indicator
                if (isFirstInPathway)
                  Container(
                    margin: EdgeInsets.only(right: 8),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                          Border.all(color: Colors.grey.shade400, width: 0.5),
                      color: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),

        // Node text
        Row(
          children: [
            if (!isFirstInPathway)
              Positioned(
                left: 0,
                top: 12,
                width: 10, // Length of horizontal connector
                height: 1,
                child: CustomPaint(
                  size: Size(15, 1),
                  painter: HorizontalDottedLinePainter(
                    color: Colors.grey.shade400,
                    dashLength: 3,
                    dashGap: 3,
                  ),
                ),
              ),
            SizedBox(width: 10),
            Text(
              childNode['text'] ?? '',
              style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                  fontWeight: FontWeight.w400,
                  color: Colors.black),
            ),
          ],
        ),
      ],
    );
  }

  // Build parallel group as a column within the row
// Build parallel group as a column within the row
  Widget _buildParallelGroupInRow(List<Map<String, dynamic>> parallelItems) {
    return Stack(
      children: [
        // Main vertical line from first LO downward
        if (parallelItems.length > 1)
          Positioned(
            left: 3, // Center of first circle
            top: 16, // Start from bottom of first circle
            height: (parallelItems.length - 1) *
                24, // Extend to cover all remaining LOs
            child: CustomPaint(
              size: Size(1, (parallelItems.length - 1) * 24),
              painter: DottedLinePainter(
                color: Colors.grey.shade400,
                dashLength: 3,
                dashGap: 3,
              ),
            ),
          ),

        // Horizontal branch lines from vertical line to each LO (except first)
        for (int i = 1; i < parallelItems.length; i++)
          Positioned(
            left: 3, // Start from the vertical line
            top: (i * 24) + 16, // Align with each circle's center
            width: 40, // Extend to just before the circle
            height: 1,
            child: CustomPaint(
              size: Size(43, 1),
              painter: HorizontalDottedLinePainter(
                color: Colors.grey.shade400,
                dashLength: 3,
                dashGap: 3,
              ),
            ),
          ),

        // The actual parallel LO nodes
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < parallelItems.length; i++) ...[
              if (i > 0) SizedBox(height: 8),

              // Parallel LO node
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (i > 0) SizedBox(width: 35),
                  // Circle and connectors
                  Stack(
                    children: [
                      // Original horizontal connector only for first LO
                      if (i == 0)
                        Positioned(
                          left: -46,
                          top: 8,
                          width: 55,
                          height: 1,
                          child: CustomPaint(
                            size: Size(55, 1),
                            painter: HorizontalDottedLinePainter(
                              color: Colors.orange.shade300,
                              dashLength: 3,
                              dashGap: 3,
                            ),
                          ),
                        ),

                      Row(
                        children: [
                          // Circle indicator
                          Container(
                            margin: EdgeInsets.only(right: 8),
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                  color: Colors.grey.shade400, width: 0.5),
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // LO text
                  Text(
                    parallelItems[i]['text'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'TiemposText',
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ],
    );
  }

  // Build agent table similar to web_home_screen_chat.dart
  Widget _buildAgentTable(ManualCreationProvider provider) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
                vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
            decoration: BoxDecoration(
              // color: Colors.grey.shade50,
              border: Border.all(color: Color(0xffD0D0D0)),
            ),
            child: Row(
              children: [
                Text(
                  'Roles',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    color: Color(0xffFFE5B4),
                    borderRadius: BorderRadius.circular(AppSpacing.xxs),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate:
                      _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  coreResponsibilities: agent.sections
                      .where((section) => section.title
                          .toLowerCase()
                          .contains('responsibilities'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                  // Extract permissions from agent sections
                  kpis: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('performance'))
                      .expand((section) => section.items)
                      .toList(),
                  decisionAuthority: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('authority'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                );

                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                    ),
                  ),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: provider.selectedRole?.id == role.id,
                    isBorderRequired: false,
                    isHoverCardRequired: false,
                    selectedColor: Color(0xffF2F2F2),
                    onRoleTap: (selectedRole) {
                      provider.showRoleDetailsPanel(selectedRole);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return _IconWithTooltip(
      iconPath: iconPath,
      tooltipText: tooltipText,
      isSelected:
          _activeIconView == tooltipText, // Check if this icon is selected
      onTap: () {
        // Set the active icon view
        setState(() {
          _activeIconView = tooltipText;
        });
        // Call the original onTap if needed
        onTap();
      },
    );
  }
}

class _IconWithTooltip extends StatefulWidget {
  final String iconPath;
  final String tooltipText;
  final VoidCallback onTap;
  final bool isSelected;

  const _IconWithTooltip({
    required this.iconPath,
    required this.tooltipText,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_IconWithTooltip> createState() => _IconWithTooltipState();
}

// Custom painter for dotted vertical lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    this.dashLength = 5.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for dotted horizontal lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    this.dashLength = 3.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _IconWithTooltipState extends State<_IconWithTooltip> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width + 8, // 8px gap from icon
          top: position.dy + (size.height / 2) - 18, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xxs,
              ),
              child: Text(
                widget.tooltipText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      print('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the icon color based on selected and hover states
    Color iconColor;
    if (widget.isSelected) {
      iconColor = Color(0xff0058FF); // Blue when selected
    } else if (isHovered) {
      iconColor = Color(0xff0058FF); // Blue when hovered
    } else {
      iconColor = Color(0xff797676); // Default gray
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() => isHovered = true);
        _showTooltip();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _removeTooltip();
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          key: _iconKey,
          padding: EdgeInsets.all(4), // Add padding for background
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Color(0xff0058FF)
                    .withOpacity(0.1) // Light blue background when selected
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: SvgPicture.asset(
            widget.iconPath,
            width: 11,
            height: 11,
            color: iconColor,
            // Add stroke width for bold effect when selected
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
