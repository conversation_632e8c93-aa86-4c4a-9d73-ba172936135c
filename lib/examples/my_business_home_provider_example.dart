import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/my_business_home_provider.dart';

/// Example demonstrating how to use MyBusinessHomeProvider
class MyBusinessHomeProviderExample extends StatelessWidget {
  const MyBusinessHomeProviderExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Business Home Provider Example'),
      ),
      body: Consumer<MyBusinessHomeProvider>(
        builder: (context, provider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Action Cards Data Management',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                
                // Display total cards count
                Text('Total Action Cards: ${provider.actionCardsData.length}'),
                Text('Favorite Cards: ${provider.favoriteItems.length}'),
                Text('Page Count: ${provider.pageCount}'),
                
                SizedBox(height: 16),
                
                // Search functionality
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Search Action Cards',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    provider.searchActionCards(value);
                  },
                ),
                
                SizedBox(height: 16),
                
                // Display filtered results
                if (provider.searchQuery.isNotEmpty) ...[
                  Text('Search Results (${provider.filteredActionCards.length}):'),
                  SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: provider.filteredActionCards.length,
                      itemBuilder: (context, index) {
                        final card = provider.filteredActionCards[index];
                        return ListTile(
                          title: Text(card['text']),
                          subtitle: Text(card['action']),
                          trailing: IconButton(
                            icon: Icon(
                              card['isSelected'] ? Icons.favorite : Icons.favorite_border,
                              color: card['isSelected'] ? Colors.red : Colors.grey,
                            ),
                            onPressed: () {
                              final originalIndex = provider.actionCardsData.indexOf(card);
                              provider.toggleFavorite(originalIndex);
                            },
                          ),
                          onTap: () {
                            provider.handleActionCardTap(card['action']);
                          },
                        );
                      },
                    ),
                  ),
                ] else ...[
                  // Display all action cards
                  Text('All Action Cards:'),
                  SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: provider.actionCardsData.length,
                      itemBuilder: (context, index) {
                        final card = provider.actionCardsData[index];
                        return ListTile(
                          title: Text(card['text']),
                          subtitle: Text(card['action']),
                          trailing: IconButton(
                            icon: Icon(
                              card['isSelected'] ? Icons.favorite : Icons.favorite_border,
                              color: card['isSelected'] ? Colors.red : Colors.grey,
                            ),
                            onPressed: () {
                              provider.toggleFavorite(index);
                            },
                          ),
                          onTap: () {
                            provider.handleActionCardTap(card['action']);
                          },
                        );
                      },
                    ),
                  ),
                ],
                
                SizedBox(height: 16),
                
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        provider.setAllAsFavorites();
                      },
                      child: Text('Set All as Favorites'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        provider.resetAllFavorites();
                      },
                      child: Text('Reset All Favorites'),
                    ),
                  ],
                ),
                
                SizedBox(height: 8),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        provider.clearSearch();
                      },
                      child: Text('Clear Search'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Example of adding a new action card
                        provider.addActionCard({
                          'text': 'New Action Card',
                          'icon': 'add',
                          'image': 'assets/images/my_business/solutions/solution_apply.png',
                          'action': 'new_action',
                          'isSelected': false,
                        });
                      },
                      child: Text('Add New Card'),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
