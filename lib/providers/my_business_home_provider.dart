import 'package:flutter/material.dart';
import 'base_provider.dart';

/// Provider for managing My Business Home screen state and operations
class MyBusinessHomeProvider extends BaseProvider {
  // Action cards data
  List<Map<String, dynamic>> _actionCardsData = [
    {
      'text': 'Procurement Management',
      'icon': 'shopping_cart',
      'image': "assets/images/my_business/solutions/solution_apply.png",
      'action': 'procurement_management',
      'isSelected': true
    },
    {
      'text': 'My Modules and My team',
      'icon': 'people',
      'image': "assets/images/my_business/solutions/solution_employee_details.png",
      'action': 'modules_team',
      'isSelected': true
    },
    {
      'text': 'Org Guides and Business Modules',
      'icon': 'business',
      'image': "assets/images/my_business/solutions/solution_my_module.png",
      'action': 'organisation_guides',
      'isSelected': true
    },
    {
      'text': 'Leave management',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_my_attendance.png",
      'action': 'leave_management',
      'isSelected': true
    },
    {
      'text': 'Apply Leave',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_organisation_details.png",
      'action': 'apply_leave',
      'isSelected': true
    },
    {
      'text': 'Leave approval',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_product_details.png",
      'action': 'leave_approval',
      'isSelected': true
    },
    {
      'text': 'Performance Review',
      'icon': 'assessment',
      'image': "assets/images/my_business/solutions/solution_payment_refund.png",
      'action': 'performance_review',
      'isSelected': true
    },
    {
      'text': 'Training & Development',
      'icon': 'school',
      'image': "assets/images/my_business/solutions/solution_my_orders.png",
      'action': 'training_development',
      'isSelected': true
    },
    {
      'text': 'Training & Development',
      'icon': 'school',
      'image': "assets/images/my_business/solutions/solution_my_orders.png",
      'action': 'training_development',
      'isSelected': false
    },
  ];

  // Label widgets data
  final List<Map<String, dynamic>> _labelWidgetsData = [
    {'text': 'Latest News', 'action': 'latest_news'},
    {'text': 'Approve/Reject PO', 'action': 'approve_reject_po'},
    {'text': 'Apply Leave', 'action': 'apply_leave'},
    {'text': 'Expense Claims', 'action': 'expense_claims'},
    {'text': 'Timesheet Entry', 'action': 'timesheet_entry'},
    {'text': 'Travel Request', 'action': 'travel_request'},
  ];

  // Pagination and layout constants
  static const int cardsPerRow = 3;
  static const int cardsPerPage = 6; // 2 rows × 3 cards each
  static const int labelsPerPage = 2;
  static const double gap = 12;

  // Current page tracking
  int _currentPage = 0;
  
  // Page controllers
  PageController? _pageController = PageController();
  PageController? _labelPageController ;

  // Search functionality
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredActionCards = [];

  // Getters
  List<Map<String, dynamic>> get actionCardsData => _actionCardsData;
  List<Map<String, dynamic>> get labelWidgetsData => _labelWidgetsData;
  List<Map<String, dynamic>> get favoriteItems => 
      _actionCardsData.where((item) => item['isSelected'] == true).toList();
  List<Map<String, dynamic>> get filteredActionCards => 
      _searchQuery.isEmpty ? _actionCardsData : _filteredActionCards;
  
  int get currentPage => _currentPage;
  int get pageCount => (favoriteItems.length / cardsPerPage).ceil();
  String get searchQuery => _searchQuery;
  
  PageController? get pageController => _pageController;
  PageController? get labelPageController => _labelPageController;

  // Initialize page controllers
  void initializeControllers() {
    _pageController = PageController();
    _labelPageController = PageController();
  }

  // Dispose page controllers
  void disposeControllers() {
    _pageController?.dispose();
    _labelPageController?.dispose();
    _pageController = null;
    _labelPageController = null;
  }

  // Toggle favorite status of an action card
  void toggleFavorite(int index) {
    if (index >= 0 && index < _actionCardsData.length) {
      _actionCardsData[index]['isSelected'] = !_actionCardsData[index]['isSelected'];
      notifyListeners();
    }
  }

  // Update current page
  void updateCurrentPage(int page) {
    if (_currentPage != page) {
      _currentPage = page;
      notifyListeners();
    }
  }

  // Search action cards
  void searchActionCards(String query) {
    _searchQuery = query.toLowerCase().trim();
    _applySearchFilter();
    notifyListeners();
  }

  // Clear search
  void clearSearch() {
    _searchQuery = '';
    _filteredActionCards.clear();
    notifyListeners();
  }

  // Apply search filter
  void _applySearchFilter() {
    if (_searchQuery.isEmpty) {
      _filteredActionCards.clear();
    } else {
      _filteredActionCards = _actionCardsData.where((item) {
        final text = item['text']?.toString().toLowerCase() ?? '';
        final action = item['action']?.toString().toLowerCase() ?? '';
        return text.contains(_searchQuery) || action.contains(_searchQuery);
      }).toList();
    }
  }

  // Get action card by action
  Map<String, dynamic>? getActionCardByAction(String action) {
    try {
      return _actionCardsData.firstWhere((card) => card['action'] == action);
    } catch (e) {
      return null;
    }
  }

  // Get favorite action cards
  List<Map<String, dynamic>> getFavoriteActionCards() {
    return _actionCardsData.where((item) => item['isSelected'] == true).toList();
  }

  // Get non-favorite action cards
  List<Map<String, dynamic>> getNonFavoriteActionCards() {
    return _actionCardsData.where((item) => item['isSelected'] != true).toList();
  }

  // Add new action card
  void addActionCard(Map<String, dynamic> card) {
    _actionCardsData.add(card);
    notifyListeners();
  }

  // Remove action card by index
  void removeActionCard(int index) {
    if (index >= 0 && index < _actionCardsData.length) {
      _actionCardsData.removeAt(index);
      notifyListeners();
    }
  }

  // Update action card
  void updateActionCard(int index, Map<String, dynamic> updatedCard) {
    if (index >= 0 && index < _actionCardsData.length) {
      _actionCardsData[index] = updatedCard;
      notifyListeners();
    }
  }

  // Reset all favorites
  void resetAllFavorites() {
    for (var card in _actionCardsData) {
      card['isSelected'] = false;
    }
    notifyListeners();
  }

  // Set all as favorites
  void setAllAsFavorites() {
    for (var card in _actionCardsData) {
      card['isSelected'] = true;
    }
    notifyListeners();
  }

  // Handle action card tap
  void handleActionCardTap(String action) {
    debugPrint('$action tapped');
    // Add your navigation or action handling logic here
  }

  // Handle label widget tap
  void handleLabelWidgetTap(String action) {
    debugPrint('$action tapped');
    // Add your navigation or action handling logic here
  }

  @override
  void dispose() {
    disposeControllers();
    super.dispose();
  }
}
