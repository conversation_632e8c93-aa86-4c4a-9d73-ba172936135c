import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class ChatSession {
  final String id;
  final String title;
  final String date;

  ChatSession({
    required this.id,
    required this.title,
    required this.date,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      date: json['date'] ?? '',
    );
  }
}

class ChatHistoryProvider extends ChangeNotifier {
  List<ChatSession> _allSessions = [];
  List<ChatSession> _currentPageSessions = [];
  int _currentPage = 1;
  final int _itemsPerPage = 8;
  bool _isLoading = false;
  String? _selectedSessionId;

  // Getters
  List<ChatSession> get currentPageSessions => _currentPageSessions;
  int get currentPage => _currentPage;
  int get itemsPerPage => _itemsPerPage;
  bool get isLoading => _isLoading;
  String? get selectedSessionId => _selectedSessionId;
  
  int get totalPages => (_allSessions.length / _itemsPerPage).ceil();
  bool get canGoToPreviousPage => _currentPage > 1;
  bool get canGoToNextPage => _currentPage < totalPages;
  int get totalSessions => _allSessions.length;

  ChatHistoryProvider() {
    loadChatSessions();
  }

  Future<void> loadChatSessions() async {
    _isLoading = true;
    notifyListeners();

    try {
      final String response = await rootBundle.loadString('assets/data/chat_sessions.json');
      final Map<String, dynamic> data = json.decode(response);
      final List<dynamic> sessionsJson = data['chat_sessions'] ?? [];
      
      _allSessions = sessionsJson
          .map((sessionJson) => ChatSession.fromJson(sessionJson))
          .toList();
      
      _updateCurrentPageSessions();
    } catch (e) {
      debugPrint('Error loading chat sessions: $e');
      _allSessions = [];
      _currentPageSessions = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _updateCurrentPageSessions() {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;
    
    if (startIndex < _allSessions.length) {
      _currentPageSessions = _allSessions.sublist(
        startIndex,
        endIndex > _allSessions.length ? _allSessions.length : endIndex,
      );
    } else {
      _currentPageSessions = [];
    }
  }

  void goToNextPage() {
    if (canGoToNextPage) {
      _currentPage++;
      _updateCurrentPageSessions();
      notifyListeners();
    }
  }

  void goToPreviousPage() {
    if (canGoToPreviousPage) {
      _currentPage--;
      _updateCurrentPageSessions();
      notifyListeners();
    }
  }

  void goToPage(int page) {
    if (page >= 1 && page <= totalPages) {
      _currentPage = page;
      _updateCurrentPageSessions();
      notifyListeners();
    }
  }

  void selectSession(String sessionId) {
    _selectedSessionId = sessionId;
    notifyListeners();
  }

  void clearSelection() {
    _selectedSessionId = null;
    notifyListeners();
  }
}
