{"success": true, "message": "Workflow validation successful", "data": {"parsed_data": {"go": {"global_objectives": {"name": "Purchase Requisition Process", "description": "Complete purchase requisition workflow from request to approval", "primary_entity": "Purchase Request"}, "process_flow": [{"lo_name": "System Verifies Budget Availability", "actor_type": "System", "description": "Automated check for budget availability", "route_type": "Sequential", "id": "step1", "go_id": "go1", "lo_id": "lo1", "routes": ["step2"]}, {"lo_name": "Requester Creates Purchase Requisition", "actor_type": "User", "description": "User creates a new purchase requisition", "route_type": "Sequential", "id": "step2", "go_id": "go1", "lo_id": "lo2", "routes": ["step3"]}, {"lo_name": "System Validates Purchase Requisition", "actor_type": "System", "description": "System validates the purchase requisition data", "route_type": "Alternate", "id": "step3", "go_id": "go1", "lo_id": "lo3", "conditions": [{"condition": "Amount > $10,000", "route_to": "step4"}, {"condition": "Amount <= $10,000", "route_to": "step5"}]}, {"lo_name": "Manager Reviews Purchase Requisition", "actor_type": "Manager", "description": "Manager reviews high-value purchase requisition", "route_type": "Alternate", "id": "step4", "go_id": "go1", "lo_id": "lo4", "conditions": [{"condition": "Amount > $50,000", "route_to": "step6"}, {"condition": "Approved", "route_to": "step7"}, {"condition": "Rejected", "route_to": "step8"}]}, {"lo_name": "Supervisor Reviews Purchase Requisition", "actor_type": "Supervisor", "description": "Supervisor reviews low-value purchase requisition", "route_type": "Sequential", "id": "step5", "go_id": "go1", "lo_id": "lo5", "routes": ["step7"]}, {"lo_name": "Director Reviews High-Value Purchase Requisition", "actor_type": "Director", "description": "Director approval required for high-value purchases", "route_type": "Alternate", "id": "step6", "go_id": "go1", "lo_id": "lo6", "conditions": [{"condition": "Approved", "route_to": "step7"}, {"condition": "Rejected", "route_to": "step8"}]}, {"lo_name": "Procurement Officer Creates Purchase Order", "actor_type": "Procurement Officer", "description": "Create purchase order after approval", "route_type": "<PERSON><PERSON><PERSON>", "id": "step7", "go_id": "go1", "lo_id": "lo7", "parallel_routes": [{"route_to": "step9", "description": "Inventory Management"}, {"route_to": "step10", "description": "Finance Processing"}, {"route_to": "step11", "description": "Vendor Notification"}], "join_at": "step12"}, {"lo_name": "System Notifies Requester Of Rejection", "actor_type": "System", "description": "Send rejection notification to requester", "route_type": "Sequential", "id": "step8", "go_id": "go1", "lo_id": "lo8", "routes": []}, {"lo_name": "Inventory Manager Checks Stock Availability", "actor_type": "Inventory Manager", "description": "Check if items are available in stock", "route_type": "Sequential", "id": "step9", "go_id": "go1", "lo_id": "lo9", "routes": ["step12"]}, {"lo_name": "Finance Officer Allocates Budget", "actor_type": "Finance Officer", "description": "Allocate budget for the purchase", "route_type": "Sequential", "id": "step10", "go_id": "go1", "lo_id": "lo10", "routes": ["step12"]}, {"lo_name": "System Notifies Vendor", "actor_type": "System", "description": "Send purchase order to vendor", "route_type": "Sequential", "id": "step11", "go_id": "go1", "lo_id": "lo11", "routes": ["step12"]}, {"lo_name": "System Completes Purchase Order", "actor_type": "System", "description": "Finalize the purchase order process", "route_type": "Sequential", "id": "step12", "go_id": "go1", "lo_id": "lo12", "routes": []}]}}}}