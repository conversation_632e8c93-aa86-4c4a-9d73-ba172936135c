                        -HD:\SDKS\sdk\flutter_windows_3.29.2-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\SDKS\Android\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\SDKS\Android\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\SDKS\Android\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\SDKS\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\DurgaPrasad\WorkSpace\BSB\GitBraneWs\design-time-ui-flutter\build\app\intermediates\cxx\Debug\1p5t3639\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\DurgaPrasad\WorkSpace\BSB\GitBraneWs\design-time-ui-flutter\build\app\intermediates\cxx\Debug\1p5t3639\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\DurgaPrasad\WorkSpace\BSB\GitBraneWs\design-time-ui-flutter\android\app\.cxx\Debug\1p5t3639\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2