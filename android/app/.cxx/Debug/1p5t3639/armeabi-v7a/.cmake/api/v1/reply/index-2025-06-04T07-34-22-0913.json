{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/SDKS/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/SDKS/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/SDKS/Android/cmake/3.22.1/bin/ctest.exe", "root": "D:/SDKS/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-76a24588640cf244a54f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-93c6da0d3ebae501c335.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c628e1395bd02bce6c10.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-93c6da0d3ebae501c335.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c628e1395bd02bce6c10.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-76a24588640cf244a54f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}