{"buildFiles": ["D:\\SDKS\\sdk\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\SDKS\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DurgaPrasad\\WorkSpace\\BSB\\GitBraneWs\\design-time-ui-flutter\\android\\app\\.cxx\\Debug\\1p5t3639\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\SDKS\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DurgaPrasad\\WorkSpace\\BSB\\GitBraneWs\\design-time-ui-flutter\\android\\app\\.cxx\\Debug\\1p5t3639\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}